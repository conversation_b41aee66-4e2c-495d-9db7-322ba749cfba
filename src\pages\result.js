// Import utilities for assessment details and charts
import { RIASEC_DETAILS, OCEAN_DETAILS, VIA_IS_CATEGORIES, VIA_IS_STRENGTH_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Dashboard
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Hasil Assessment</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="downloadResult()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Download PDF
              </button>
              <button onclick="shareResult()" class="text-gray-500 hover:text-gray-700">
                Bagikan
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">Profil Persona Anda</h2>
              <p class="text-gray-600 mt-1">Assessment diselesaikan pada: <span id="completion-date"></span></p>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-500 mb-1">Risk Tolerance</div>
              <div class="text-lg font-bold text-indigo-600 capitalize" id="risk-tolerance">Moderate</div>
            </div>
          </div>
        </div>

        <!-- Quick Navigation to Detailed Pages -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Jelajahi Hasil Assessment Detail</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button onclick="navigateTo('result-via-is')"
              class="bg-purple-50 border border-purple-200 rounded-lg p-4 hover:bg-purple-100 transition duration-200 text-left">
              <div class="text-2xl mb-2">💎</div>
              <h4 class="font-semibold text-purple-800">VIA-IS</h4>
              <p class="text-purple-600 text-sm">Character Strengths</p>
            </button>
            <button onclick="navigateTo('result-riasec')"
              class="bg-blue-50 border border-blue-200 rounded-lg p-4 hover:bg-blue-100 transition duration-200 text-left">
              <div class="text-2xl mb-2">🎯</div>
              <h4 class="font-semibold text-blue-800">RIASEC</h4>
              <p class="text-blue-600 text-sm">Holland Codes</p>
            </button>
            <button onclick="navigateTo('result-ocean')"
              class="bg-green-50 border border-green-200 rounded-lg p-4 hover:bg-green-100 transition duration-200 text-left">
              <div class="text-2xl mb-2">🌊</div>
              <h4 class="font-semibold text-green-800">OCEAN</h4>
              <p class="text-green-600 text-sm">Big Five Personality</p>
            </button>
            <button onclick="navigateTo('result-persona')"
              class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 hover:bg-indigo-100 transition duration-200 text-left">
              <div class="text-2xl mb-2">🧠</div>
              <h4 class="font-semibold text-indigo-800">Persona Profile</h4>
              <p class="text-indigo-600 text-sm">Profil Lengkap</p>
            </button>
          </div>
        </div>

        <!-- Assessment Results Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">Hasil Assessment Psikometri</h3>

          <!-- Assessment Explanations -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                <span class="text-blue-500 mr-2">🎯</span>
                RIASEC
              </h4>
              <p class="text-blue-700 text-sm mb-2">Holland Codes - Minat Karir</p>
              <p class="text-blue-600 text-xs leading-relaxed">
                Mengukur 6 tipe kepribadian kerja: Realistic (praktis), Investigative (analitis),
                Artistic (kreatif), Social (sosial), Enterprising (wirausaha), Conventional (terorganisir).
              </p>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 class="font-semibold text-green-800 mb-2 flex items-center">
                <span class="text-green-500 mr-2">🌊</span>
                OCEAN
              </h4>
              <p class="text-green-700 text-sm mb-2">Big Five Personality</p>
              <p class="text-green-600 text-xs leading-relaxed">
                Mengukur 5 dimensi kepribadian: Openness (keterbukaan), Conscientiousness (kehati-hatian),
                Extraversion (ekstraversi), Agreeableness (keramahan), Neuroticism (neurotisisme).
              </p>
            </div>

            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 class="font-semibold text-purple-800 mb-2 flex items-center">
                <span class="text-purple-500 mr-2">💎</span>
                VIA-IS
              </h4>
              <p class="text-purple-700 text-sm mb-2">Character Strengths</p>
              <p class="text-purple-600 text-xs leading-relaxed">
                Mengidentifikasi 24 kekuatan karakter dalam 6 kategori: Wisdom, Courage, Humanity,
                Justice, Temperance, dan Transcendence untuk pengembangan diri optimal.
              </p>
            </div>
          </div>

          <!-- Radar Chart Section -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Profil Kepribadian (OCEAN)</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="oceanRadarChart" width="300" height="300"></canvas>
              </div>
            </div>

            <div>
              <h4 class="font-medium text-gray-900 mb-4">Minat Karir (RIASEC)</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="riasecRadarChart" width="300" height="300"></canvas>
              </div>
            </div>
          </div>

          <!-- Top Character Strengths -->
          <div class="mt-6">
            <h4 class="font-medium text-gray-900 mb-4">Top 5 Character Strengths (VIA-IS)</h4>
            <div class="grid grid-cols-1 md:grid-cols-5 gap-3" id="top-strengths">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- VIA-IS Categories -->
          <div class="mt-8">
            <h4 class="font-medium text-gray-900 mb-6">Character Strengths by Categories (VIA-IS)</h4>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="via-is-categories">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- VIA-IS Visualizations -->
          <div class="mt-8">
            <h4 class="font-medium text-gray-900 mb-6">VIA-IS Character Strengths Visualization</h4>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h5 class="font-medium text-gray-700 mb-4">Categories Overview</h5>
                <div class="bg-gray-50 rounded-lg p-4">
                  <canvas id="viaIsCategoriesRadarChart" width="300" height="300"></canvas>
                </div>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-4">All Character Strengths</h5>
                <div class="bg-gray-50 rounded-lg p-4" style="height: 300px; overflow-y: auto;">
                  <canvas id="viaIsStrengthsBarChart" width="300" height="600"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Assessment Interpretations -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">Interpretasi Detail Assessment</h3>

          <!-- RIASEC Details -->
          <div class="mb-8">
            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <span class="text-blue-500 mr-2">🎯</span>
              Detail RIASEC Holland Codes
            </h4>
            <div id="riasec-details" class="space-y-4">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- OCEAN Details -->
          <div class="mb-8">
            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <span class="text-green-500 mr-2">🌊</span>
              Detail OCEAN Big Five Personality
            </h4>
            <div id="ocean-details" class="space-y-4">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- VIA-IS Details -->
          <div>
            <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <span class="text-purple-500 mr-2">💎</span>
              Detail VIA-IS Character Strengths
            </h4>
            <div id="via-is-details" class="space-y-4">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Archetype Section -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow p-6 mb-6 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-3xl">🧠</span>
            </div>
            <div class="flex-1">
              <h3 class="text-2xl font-bold mb-2" id="archetype">The Analytical Innovator</h3>
              <p class="text-indigo-100 leading-relaxed" id="short-summary">
                Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi.
              </p>
            </div>
          </div>
        </div>

        <!-- Strengths and Weaknesses -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-green-500 mr-2">💪</span>
              Kekuatan Utama
            </h3>
            <p class="text-gray-600 text-sm mb-4" id="strength-summary">
              Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru.
            </p>
            <div class="space-y-3" id="strengths-list">
              <!-- Strengths will be populated by JavaScript -->
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-orange-500 mr-2">🎯</span>
              Area Pengembangan
            </h3>
            <p class="text-gray-600 text-sm mb-4" id="weakness-summary">
              Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat.
            </p>
            <div class="space-y-3" id="weaknesses-list">
              <!-- Weaknesses will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Career Recommendations -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="text-blue-500 mr-2">🚀</span>
            Rekomendasi Karir
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="career-recommendations">
            <!-- Career recommendations will be populated by JavaScript -->
          </div>
        </div>

        <!-- Work Environment -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="text-purple-500 mr-2">🏢</span>
            Lingkungan Kerja Ideal
          </h3>
          <p class="text-gray-600 leading-relaxed" id="work-environment">
            Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan.
          </p>
        </div>

        <!-- Insights and Skill Suggestions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-yellow-500 mr-2">💡</span>
              Insights & Saran
            </h3>
            <div class="space-y-3" id="insights-list">
              <!-- Insights will be populated by JavaScript -->
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-indigo-500 mr-2">🎓</span>
              Skill yang Disarankan
            </h3>
            <div class="flex flex-wrap gap-2" id="skill-suggestions">
              <!-- Skills will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Possible Pitfalls -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
          <h3 class="text-lg font-semibold text-red-800 mb-4 flex items-center">
            <span class="text-red-500 mr-2">⚠️</span>
            Potensi Jebakan yang Perlu Diwaspadai
          </h3>
          <div class="space-y-3" id="pitfalls-list">
            <!-- Pitfalls will be populated by JavaScript -->
          </div>
        </div>

        <!-- Role Models -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="text-amber-500 mr-2">⭐</span>
            Role Model yang Menginspirasi
          </h3>
          <div class="flex flex-wrap gap-4" id="role-models">
            <!-- Role models will be populated by JavaScript -->
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button onclick="retakeAssessment()" 
            class="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            Ulangi Assessment
          </button>
          <button onclick="scheduleConsultation()" 
            class="flex-1 bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Jadwalkan Konsultasi
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResult() {
  // Set completion date
  const completionDate = new Date().toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const dateElement = document.getElementById('completion-date');
  if (dateElement) {
    dateElement.textContent = completionDate;
  }

  // Load persona profile data
  loadPersonaProfile();
}

// Sample persona profile data - in real app this would come from API or localStorage
const personaProfileExample = {
  archetype: "The Analytical Innovator",
  shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
  strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
  strengths: [
    "Kemampuan analisis yang tajam",
    "Kreativitas dan inovasi",
    "Keingintahuan intelektual yang tinggi",
    "Kemampuan belajar mandiri yang kuat",
    "Pemikiran sistematis dan terstruktur"
  ],
  weaknessSummary: "Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat atau bekerja sama dengan orang lain.",
  weaknesses: [
    "Terkadang terlalu perfeksionis",
    "Dapat terjebak dalam overthinking",
    "Kurang sabar dengan proses yang lambat",
    "Kemampuan sosial yang perlu dikembangkan",
    "Kesulitan mendelegasikan tugas"
  ],
  careerRecommendation: [
    {
      careerName: "Data Scientist",
      careerProspect: {
        jobAvailability: "high",
        salaryPotential: "high",
        careerProgression: "high",
        industryGrowth: "super high",
        skillDevelopment: "super high"
      }
    },
    {
      careerName: "Peneliti",
      careerProspect: {
        jobAvailability: "moderate",
        salaryPotential: "moderate",
        careerProgression: "moderate",
        industryGrowth: "moderate",
        skillDevelopment: "high"
      }
    },
    {
      careerName: "Pengembang Software",
      careerProspect: {
        jobAvailability: "super high",
        salaryPotential: "high",
        careerProgression: "high",
        industryGrowth: "super high",
        skillDevelopment: "super high"
      }
    }
  ],
  insights: [
    "Kembangkan keterampilan komunikasi untuk menyampaikan ide kompleks dengan lebih efektif",
    "Latih kemampuan bekerja dalam tim untuk mengimbangi kecenderungan bekerja sendiri",
    "Manfaatkan kekuatan analitis untuk memecahkan masalah sosial",
    "Cari mentor yang dapat membantu mengembangkan keterampilan kepemimpinan",
    "Tetapkan batas waktu untuk menghindari analisis berlebihan"
  ],
  skillSuggestion: [
    "Public Speaking",
    "Leadership",
    "Teamwork",
    "Time Management",
    "Delegation"
  ],
  possiblePitfalls: [
    "Mengisolasi diri dari tim karena terlalu fokus pada analisis individu",
    "Menunda keputusan karena perfeksionisme berlebihan",
    "Kurang membangun jaringan karena terlalu fokus pada teknis"
  ],
  riskTolerance: "moderate",
  workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
  roleModel: [
    "Marie Curie",
    "Albert Einstein",
    "B.J. Habibie"
  ]
};



// Sample assessment results data
const sampleAssessmentResults = {
  ocean: {
    openness: 85,
    conscientiousness: 78,
    extraversion: 45,
    agreeableness: 62,
    neuroticism: 35
  },
  riasec: {
    realistic: 25,
    investigative: 92,
    artistic: 78,
    social: 45,
    enterprising: 55,
    conventional: 68
  },
  viaIs: {
    creativity: 88,
    curiosity: 92,
    judgment: 85,
    loveOfLearning: 90,
    perspective: 82,
    bravery: 65,
    perseverance: 78,
    honesty: 75,
    zest: 58,
    love: 62,
    kindness: 68,
    socialIntelligence: 55,
    teamwork: 48,
    fairness: 72,
    leadership: 65,
    forgiveness: 58,
    humility: 62,
    prudence: 85,
    selfRegulation: 75,
    appreciationOfBeauty: 82,
    gratitude: 68,
    hope: 72,
    humor: 55,
    spirituality: 45
  }
};

function loadPersonaProfile() {
  try {
    // Try to load from localStorage first, fallback to example data
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : personaProfileExample;

    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : sampleAssessmentResults;

    console.log('Persona profile loaded:', profile);
    console.log('Assessment results loaded:', assessmentResults);

    displayPersonaProfile(profile);
    displayAssessmentResults(assessmentResults);
  } catch (error) {
    console.error('Error loading persona profile:', error);
    // Fallback to example data
    displayPersonaProfile(personaProfileExample);
    displayAssessmentResults(sampleAssessmentResults);
  }
}

function displayPersonaProfile(profile) {
  // Update archetype and summary
  const archetypeElement = document.getElementById('archetype');
  const shortSummaryElement = document.getElementById('short-summary');
  const riskToleranceElement = document.getElementById('risk-tolerance');

  if (archetypeElement) archetypeElement.textContent = profile.archetype;
  if (shortSummaryElement) shortSummaryElement.textContent = profile.shortSummary;
  if (riskToleranceElement) riskToleranceElement.textContent = profile.riskTolerance;

  // Update strength summary and list
  const strengthSummaryElement = document.getElementById('strength-summary');
  const strengthsListElement = document.getElementById('strengths-list');

  if (strengthSummaryElement) strengthSummaryElement.textContent = profile.strengthSummary;
  if (strengthsListElement) {
    strengthsListElement.innerHTML = profile.strengths.map(strength =>
      `<div class="flex items-start space-x-2">
        <span class="text-green-500 mt-1">✓</span>
        <span class="text-gray-700 text-sm">${strength}</span>
      </div>`
    ).join('');
  }

  // Update weakness summary and list
  const weaknessSummaryElement = document.getElementById('weakness-summary');
  const weaknessesListElement = document.getElementById('weaknesses-list');

  if (weaknessSummaryElement) weaknessSummaryElement.textContent = profile.weaknessSummary;
  if (weaknessesListElement) {
    weaknessesListElement.innerHTML = profile.weaknesses.map(weakness =>
      `<div class="flex items-start space-x-2">
        <span class="text-orange-500 mt-1">•</span>
        <span class="text-gray-700 text-sm">${weakness}</span>
      </div>`
    ).join('');
  }

  // Update career recommendations
  displayCareerRecommendations(profile.careerRecommendation);

  // Update work environment
  const workEnvironmentElement = document.getElementById('work-environment');
  if (workEnvironmentElement) workEnvironmentElement.textContent = profile.workEnvironment;

  // Update insights
  const insightsListElement = document.getElementById('insights-list');
  if (insightsListElement) {
    insightsListElement.innerHTML = profile.insights.map(insight =>
      `<div class="flex items-start space-x-2">
        <span class="text-yellow-500 mt-1">💡</span>
        <span class="text-gray-700 text-sm">${insight}</span>
      </div>`
    ).join('');
  }

  // Update skill suggestions
  const skillSuggestionsElement = document.getElementById('skill-suggestions');
  if (skillSuggestionsElement) {
    skillSuggestionsElement.innerHTML = profile.skillSuggestion.map(skill =>
      `<span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">${skill}</span>`
    ).join('');
  }

  // Update possible pitfalls
  const pitfallsListElement = document.getElementById('pitfalls-list');
  if (pitfallsListElement) {
    pitfallsListElement.innerHTML = profile.possiblePitfalls.map(pitfall =>
      `<div class="flex items-start space-x-2">
        <span class="text-red-500 mt-1">⚠️</span>
        <span class="text-red-700 text-sm">${pitfall}</span>
      </div>`
    ).join('');
  }

  // Update role models
  const roleModelsElement = document.getElementById('role-models');
  if (roleModelsElement) {
    roleModelsElement.innerHTML = profile.roleModel.map(model =>
      `<div class="bg-amber-50 border border-amber-200 rounded-lg px-4 py-2">
        <span class="text-amber-800 font-medium">${model}</span>
      </div>`
    ).join('');
  }
}

function displayAssessmentResults(results) {
  // Create OCEAN radar chart
  createOceanRadarChart(results.ocean);

  // Create RIASEC radar chart
  createRiasecRadarChart(results.riasec);

  // Display top VIA-IS strengths
  displayTopStrengths(results.viaIs);

  // Display VIA-IS categories
  displayViaIsCategories(results.viaIs);

  // Create VIA-IS visualizations
  createViaIsCategoriesRadarChart(results.viaIs);
  createViaIsStrengthsBarChart(results.viaIs);

  // Display detailed interpretations
  displayRiasecDetails(results.riasec);
  displayOceanDetails(results.ocean);
  displayViaIsDetails(results.viaIs);
}

function createOceanRadarChart(oceanData) {
  const ctx = document.getElementById('oceanRadarChart');
  if (!ctx) return;

  return ChartUtils.createOceanRadarChart(ctx, oceanData);
}

function createRiasecRadarChart(riasecData) {
  const ctx = document.getElementById('riasecRadarChart');
  if (!ctx) return;

  return ChartUtils.createRiasecRadarChart(ctx, riasecData);
}

function displayTopStrengths(viaIsData) {
  const topStrengthsElement = document.getElementById('top-strengths');
  if (!topStrengthsElement) return;

  // Convert VIA-IS data to array and sort by score
  const strengthsArray = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key)
  }));

  // Sort by score and get top 5
  const topStrengths = strengthsArray
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  topStrengthsElement.innerHTML = topStrengths.map((strength, index) => {
    const colors = [
      'bg-yellow-100 text-yellow-800 border-yellow-200',
      'bg-orange-100 text-orange-800 border-orange-200',
      'bg-red-100 text-red-800 border-red-200',
      'bg-purple-100 text-purple-800 border-purple-200',
      'bg-indigo-100 text-indigo-800 border-indigo-200'
    ];

    return `
      <div class="border rounded-lg p-3 text-center ${colors[index]}">
        <div class="text-lg font-bold">#${index + 1}</div>
        <div class="text-sm font-medium">${strength.displayName}</div>
        <div class="text-xs mt-1">${strength.score}%</div>
      </div>
    `;
  }).join('');
}

function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Kreativitas',
    curiosity: 'Keingintahuan',
    judgment: 'Penilaian',
    loveOfLearning: 'Cinta Belajar',
    perspective: 'Perspektif',
    bravery: 'Keberanian',
    perseverance: 'Ketekunan',
    honesty: 'Kejujuran',
    zest: 'Semangat',
    love: 'Cinta',
    kindness: 'Kebaikan',
    socialIntelligence: 'Kecerdasan Sosial',
    teamwork: 'Kerja Tim',
    fairness: 'Keadilan',
    leadership: 'Kepemimpinan',
    forgiveness: 'Pengampunan',
    humility: 'Kerendahan Hati',
    prudence: 'Kehati-hatian',
    selfRegulation: 'Pengaturan Diri',
    appreciationOfBeauty: 'Apresiasi Keindahan',
    gratitude: 'Rasa Syukur',
    hope: 'Harapan',
    humor: 'Humor',
    spirituality: 'Spiritualitas'
  };

  return displayNames[key] || key;
}

function displayViaIsCategories(viaIsData) {
  const categoriesElement = document.getElementById('via-is-categories');
  if (!categoriesElement) return;



  categoriesElement.innerHTML = Object.entries(VIA_IS_CATEGORIES).map(([, category]) => {
    // Calculate average score for this category
    const categoryStrengths = category.strengths.map(strength => ({
      name: strength,
      score: viaIsData[strength] || 0,
      displayName: getViaIsDisplayName(strength)
    }));

    const averageScore = categoryStrengths.reduce((sum, strength) => sum + strength.score, 0) / categoryStrengths.length;
    const colorClasses = ChartUtils.getViaIsCategoryColorClasses(category.color);
    const progressBarColor = ChartUtils.getViaIsCategoryProgressBarColor(category.color);

    return `
      <div class="border rounded-lg p-6 ${colorClasses}">
        <div class="flex items-center mb-4">
          <span class="text-2xl mr-3">${category.icon}</span>
          <div>
            <h5 class="font-semibold text-lg">${category.name}</h5>
            <p class="text-sm opacity-80 mt-1">${category.description}</p>
          </div>
        </div>

        <div class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">Skor Kategori</span>
            <span class="text-lg font-bold">${Math.round(averageScore)}%</span>
          </div>
          <div class="w-full bg-white bg-opacity-50 rounded-full h-3">
            <div class="${progressBarColor} h-3 rounded-full transition-all duration-500" style="width: ${averageScore}%"></div>
          </div>
        </div>

        <div class="space-y-2">
          <h6 class="font-medium text-sm mb-3">Kekuatan dalam Kategori:</h6>
          ${categoryStrengths.map(strength => `
            <div class="flex justify-between items-center py-1">
              <span class="text-sm">${strength.displayName}</span>
              <span class="text-sm font-medium">${strength.score}%</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }).join('');
}

function displayCareerRecommendations(careerRecommendations) {
  const careerRecommendationsElement = document.getElementById('career-recommendations');
  if (!careerRecommendationsElement) return;

  const getProspectColor = (level) => {
    switch (level) {
      case 'super high': return 'bg-green-500';
      case 'high': return 'bg-blue-500';
      case 'moderate': return 'bg-yellow-500';
      case 'low': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getProspectText = (level) => {
    switch (level) {
      case 'super high': return 'Sangat Tinggi';
      case 'high': return 'Tinggi';
      case 'moderate': return 'Sedang';
      case 'low': return 'Rendah';
      default: return level;
    }
  };

  careerRecommendationsElement.innerHTML = careerRecommendations.map(career => {
    const prospects = career.careerProspect;
    const avgScore = Object.values(prospects).reduce((sum, val) => {
      const score = val === 'super high' ? 5 : val === 'high' ? 4 : val === 'moderate' ? 3 : val === 'low' ? 2 : 1;
      return sum + score;
    }, 0) / Object.keys(prospects).length;

    return `
      <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        <h4 class="font-semibold text-gray-900 mb-3">${career.careerName}</h4>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">Ketersediaan Kerja</span>
            <span class="text-xs px-2 py-1 rounded-full text-white ${getProspectColor(prospects.jobAvailability)}">
              ${getProspectText(prospects.jobAvailability)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">Potensi Gaji</span>
            <span class="text-xs px-2 py-1 rounded-full text-white ${getProspectColor(prospects.salaryPotential)}">
              ${getProspectText(prospects.salaryPotential)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">Perkembangan Karir</span>
            <span class="text-xs px-2 py-1 rounded-full text-white ${getProspectColor(prospects.careerProgression)}">
              ${getProspectText(prospects.careerProgression)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">Pertumbuhan Industri</span>
            <span class="text-xs px-2 py-1 rounded-full text-white ${getProspectColor(prospects.industryGrowth)}">
              ${getProspectText(prospects.industryGrowth)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-xs text-gray-600">Pengembangan Skill</span>
            <span class="text-xs px-2 py-1 rounded-full text-white ${getProspectColor(prospects.skillDevelopment)}">
              ${getProspectText(prospects.skillDevelopment)}
            </span>
          </div>
        </div>
        <div class="mt-3 pt-3 border-t border-gray-100">
          <div class="flex items-center justify-between">
            <span class="text-xs font-medium text-gray-700">Skor Keseluruhan</span>
            <span class="text-sm font-bold text-indigo-600">${(avgScore * 20).toFixed(0)}%</span>
          </div>
        </div>
      </div>
    `;
  }).join('');
}

export function downloadResult() {
  // Simulate PDF download
  alert('Fitur download PDF akan segera tersedia');
}

export function shareResult() {
  // Simulate sharing functionality
  if (navigator.share) {
    navigator.share({
      title: 'Hasil Assessment Talenta',
      text: 'Lihat hasil assessment talenta saya',
      url: window.location.href
    });
  } else {
    // Fallback for browsers that don't support Web Share API
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      alert('Link hasil assessment telah disalin ke clipboard');
    });
  }
}

// Function to save persona profile to localStorage
export function savePersonaProfile(profile) {
  try {
    localStorage.setItem('personaProfile', JSON.stringify(profile));
    console.log('Persona profile saved to localStorage');
  } catch (error) {
    console.error('Error saving persona profile:', error);
  }
}

export function retakeAssessment() {
  if (confirm('Apakah Anda yakin ingin mengulang assessment? Hasil sebelumnya akan ditimpa.')) {
    // Clear all assessment data including persona profile
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('assessmentCompleted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastSaveResult');
    localStorage.removeItem('lastAssessmentId');
    localStorage.removeItem('personaProfile');

    // Navigate to assessment
    navigateTo('assessment');
  }
}

export function scheduleConsultation() {
  // In a real application, this would open a consultation booking form
  const personaProfile = JSON.parse(localStorage.getItem('personaProfile') || '{}');
  const archetype = personaProfile.archetype || 'Unknown';

  alert(`Fitur penjadwalan konsultasi akan segera tersedia.\n\nBerdasarkan profil "${archetype}", kami akan merekomendasikan konselor yang tepat untuk Anda.`);
}

// New ViaIS visualization functions
function createViaIsCategoriesRadarChart(viaIsData) {
  const ctx = document.getElementById('viaIsCategoriesRadarChart');
  if (!ctx) return;

  return ChartUtils.createViaIsCategoriesRadarChart(ctx, viaIsData);
}

function createViaIsStrengthsBarChart(viaIsData) {
  const ctx = document.getElementById('viaIsStrengthsBarChart');
  if (!ctx) return;

  return ChartUtils.createViaIsStrengthsBarChart(ctx, viaIsData);
}

// Display detailed RIASEC interpretation
function displayRiasecDetails(riasecData) {
  const container = document.getElementById('riasec-details');
  if (!container) return;

  // Sort RIASEC scores to show highest first
  const sortedRiasec = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, details: RIASEC_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedRiasec.map(({ value, details }) => `
    <div class="border rounded-lg p-4 ${value >= 70 ? 'border-blue-300 bg-blue-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
      <div class="flex justify-between items-start mb-3">
        <h5 class="font-semibold text-gray-900">${details.name}</h5>
        <div class="text-right">
          <div class="text-lg font-bold ${value >= 70 ? 'text-blue-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
          <div class="text-xs ${value >= 70 ? 'text-blue-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
            ${value >= 70 ? 'Tinggi' : value >= 50 ? 'Sedang' : 'Rendah'}
          </div>
        </div>
      </div>

      <p class="text-gray-700 text-sm mb-3">${details.description}</p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <h6 class="font-medium text-gray-800 mb-2">Karakteristik:</h6>
          <ul class="text-gray-600 space-y-1">
            ${details.characteristics.slice(0, 3).map(char => `<li>• ${char}</li>`).join('')}
          </ul>
        </div>
        <div>
          <h6 class="font-medium text-gray-800 mb-2">Contoh Karir:</h6>
          <ul class="text-gray-600 space-y-1">
            ${details.careerExamples.slice(0, 3).map(career => `<li>• ${career}</li>`).join('')}
          </ul>
        </div>
      </div>
    </div>
  `).join('');
}

// Display detailed OCEAN interpretation
function displayOceanDetails(oceanData) {
  const container = document.getElementById('ocean-details');
  if (!container) return;

  // Sort OCEAN scores to show highest first
  const sortedOcean = Object.entries(oceanData)
    .map(([key, value]) => ({ key, value, details: OCEAN_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedOcean.map(({ value, details }) => {
    const isHigh = value >= 60;
    const scoreData = isHigh ? details.highScore : details.lowScore;

    return `
      <div class="border rounded-lg p-4 ${value >= 70 ? 'border-green-300 bg-green-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
        <div class="flex justify-between items-start mb-3">
          <h5 class="font-semibold text-gray-900">${details.name}</h5>
          <div class="text-right">
            <div class="text-lg font-bold ${value >= 70 ? 'text-green-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
            <div class="text-xs ${value >= 70 ? 'text-green-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
              ${value >= 60 ? 'Tinggi' : value >= 40 ? 'Sedang' : 'Rendah'}
            </div>
          </div>
        </div>

        <p class="text-gray-700 text-sm mb-3">${details.description}</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h6 class="font-medium text-gray-800 mb-2">Karakteristik (${isHigh ? 'Skor Tinggi' : 'Skor Rendah'}):</h6>
            <ul class="text-gray-600 space-y-1">
              ${scoreData.characteristics.slice(0, 3).map(char => `<li>• ${char}</li>`).join('')}
            </ul>
          </div>
          <div>
            <h6 class="font-medium text-gray-800 mb-2">Implikasi:</h6>
            <ul class="text-gray-600 space-y-1">
              ${scoreData.implications.slice(0, 3).map(impl => `<li>• ${impl}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }).join('');
}

// Display detailed ViaIS interpretation
function displayViaIsDetails(viaIsData) {
  const container = document.getElementById('via-is-details');
  if (!container) return;

  // Calculate category averages
  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);

  // Sort categories by average score
  const sortedCategories = Object.entries(VIA_IS_CATEGORIES)
    .map(([key, category]) => ({
      key,
      category,
      averageScore: categoryAverages[key],
      strengths: category.strengths.map(strength => ({
        name: strength,
        score: viaIsData[strength] || 0,
        displayName: VIA_IS_STRENGTH_DETAILS[strength]?.name || strength,
        description: VIA_IS_STRENGTH_DETAILS[strength]?.description || ''
      })).sort((a, b) => b.score - a.score)
    }))
    .sort((a, b) => b.averageScore - a.averageScore);

  container.innerHTML = sortedCategories.map(({ category, averageScore, strengths }) => {
    const colorClasses = ChartUtils.getViaIsCategoryColorClasses(category.color);
    const progressBarColor = ChartUtils.getViaIsCategoryProgressBarColor(category.color);

    return `
      <div class="border rounded-lg p-4 ${colorClasses}">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <span class="text-2xl mr-3">${category.icon}</span>
            <div>
              <h5 class="font-semibold text-lg">${category.name}</h5>
              <p class="text-sm opacity-80">${category.description}</p>
            </div>
          </div>
          <div class="text-right">
            <div class="text-lg font-bold">${Math.round(averageScore)}%</div>
            <div class="text-xs">Rata-rata</div>
          </div>
        </div>

        <div class="mb-4">
          <div class="w-full bg-white bg-opacity-50 rounded-full h-2">
            <div class="${progressBarColor} h-2 rounded-full transition-all duration-500" style="width: ${averageScore}%"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          ${strengths.map(strength => `
            <div class="bg-white bg-opacity-70 rounded p-3">
              <div class="flex justify-between items-center mb-1">
                <span class="font-medium text-sm">${strength.displayName}</span>
                <span class="text-sm font-bold">${strength.score}%</span>
              </div>
              <p class="text-xs opacity-80">${strength.description}</p>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }).join('');
}
