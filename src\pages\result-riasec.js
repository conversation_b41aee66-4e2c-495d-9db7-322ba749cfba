// Import utilities for RIASEC assessment details and charts
import { RIASEC_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultRiasecPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('result-via-is')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← VIA-IS
              </button>
              <h1 class="text-xl font-semibold text-gray-900">RIASEC Holland Codes</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="navigateTo('result-ocean')" class="text-indigo-600 hover:text-indigo-700">
                OCEAN →
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg shadow p-8 mb-8 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-4xl">🎯</span>
            </div>
            <div class="flex-1">
              <h2 class="text-3xl font-bold mb-2">RIASEC Holland Codes</h2>
              <p class="text-blue-100 text-lg leading-relaxed">
                Teori Holland mengidentifikasi 6 tipe kepribadian kerja yang membantu menentukan minat karir dan lingkungan kerja yang sesuai.
              </p>
            </div>
          </div>
        </div>

        <!-- RIASEC Explanation -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Tentang RIASEC Holland Codes</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Apa itu RIASEC?</h4>
              <div class="space-y-4 text-gray-700">
                <p>
                  RIASEC adalah teori yang dikembangkan oleh psikolog John Holland yang mengklasifikasikan 
                  kepribadian dan lingkungan kerja menjadi 6 tipe utama.
                </p>
                <p>
                  Teori ini didasarkan pada premis bahwa orang akan lebih puas dan sukses dalam karir 
                  yang sesuai dengan tipe kepribadian mereka.
                </p>
                <p>
                  RIASEC merupakan singkatan dari: <strong>R</strong>ealistic, <strong>I</strong>nvestigative, 
                  <strong>A</strong>rtistic, <strong>S</strong>ocial, <strong>E</strong>nterprising, dan <strong>C</strong>onventional.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Manfaat Mengetahui Tipe RIASEC</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <span class="text-blue-500 mt-1">✓</span>
                  <span class="text-gray-700">Membantu pemilihan karir yang tepat</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-blue-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan kepuasan kerja</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-blue-500 mt-1">✓</span>
                  <span class="text-gray-700">Memahami lingkungan kerja yang ideal</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-blue-500 mt-1">✓</span>
                  <span class="text-gray-700">Mengidentifikasi kekuatan dan preferensi</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-blue-500 mt-1">✓</span>
                  <span class="text-gray-700">Panduan untuk pengembangan karir</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- RIASEC Types Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">6 Tipe Kepribadian RIASEC</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="riasec-types-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- User's RIASEC Profile -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Profil RIASEC Anda</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Radar Chart -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Visualisasi Radar Chart</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="riasecRadarChart" width="400" height="400"></canvas>
              </div>
            </div>

            <!-- Top 3 Types -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Top 3 Tipe Kepribadian Anda</h4>
              <div id="top-riasec-types" class="space-y-4">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed RIASEC Results -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Detail Hasil Assessment RIASEC</h3>
          <div id="riasec-detailed-results" class="space-y-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Career Recommendations -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Rekomendasi Karir Berdasarkan RIASEC</h3>
          <div id="riasec-career-recommendations" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Work Environment Preferences -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Preferensi Lingkungan Kerja</h3>
          <div id="work-environment-preferences" class="space-y-4">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
          <button onclick="navigateTo('result-via-is')" 
            class="bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            ← Kembali ke VIA-IS
          </button>
          <button onclick="navigateTo('result-ocean')" 
            class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Lanjut ke OCEAN →
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultRiasec() {
  // Load RIASEC assessment data
  loadRiasecData();
}

function loadRiasecData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleRiasecData();

    console.log('RIASEC assessment results loaded:', assessmentResults);

    displayRiasecData(assessmentResults.riasec || assessmentResults);
  } catch (error) {
    console.error('Error loading RIASEC data:', error);
    // Fallback to sample data
    displayRiasecData(getSampleRiasecData());
  }
}

function getSampleRiasecData() {
  return {
    realistic: 25,
    investigative: 92,
    artistic: 78,
    social: 45,
    enterprising: 55,
    conventional: 68
  };
}

function displayRiasecData(riasecData) {
  // Display types overview
  displayTypesOverview();

  // Create radar chart
  createRiasecRadarChart(riasecData);

  // Display top 3 types
  displayTopTypes(riasecData);

  // Display detailed results
  displayDetailedResults(riasecData);

  // Display career recommendations
  displayCareerRecommendations(riasecData);

  // Display work environment preferences
  displayWorkEnvironmentPreferences(riasecData);
}

function displayTypesOverview() {
  const container = document.getElementById('riasec-types-overview');
  if (!container) return;

  const riasecTypes = [
    {
      code: 'R',
      name: 'Realistic',
      description: 'Praktis, hands-on, suka bekerja dengan alat dan mesin',
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: '🔧'
    },
    {
      code: 'I',
      name: 'Investigative',
      description: 'Analitis, suka memecahkan masalah dan penelitian',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: '🔬'
    },
    {
      code: 'A',
      name: 'Artistic',
      description: 'Kreatif, ekspresif, suka seni dan inovasi',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      icon: '🎨'
    },
    {
      code: 'S',
      name: 'Social',
      description: 'Suka membantu, mengajar, dan bekerja dengan orang',
      color: 'bg-green-50 border-green-200 text-green-800',
      icon: '👥'
    },
    {
      code: 'E',
      name: 'Enterprising',
      description: 'Ambisius, suka memimpin dan berbisnis',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: '💼'
    },
    {
      code: 'C',
      name: 'Conventional',
      description: 'Terorganisir, detail, suka struktur dan aturan',
      color: 'bg-gray-50 border-gray-200 text-gray-800',
      icon: '📊'
    }
  ];

  container.innerHTML = riasecTypes.map(type => `
    <div class="border rounded-lg p-6 ${type.color}">
      <div class="flex items-center mb-4">
        <span class="text-3xl mr-4">${type.icon}</span>
        <div>
          <h4 class="font-semibold text-lg">${type.code} - ${type.name}</h4>
        </div>
      </div>
      <p class="text-sm">${type.description}</p>
    </div>
  `).join('');
}

function createRiasecRadarChart(riasecData) {
  const ctx = document.getElementById('riasecRadarChart');
  if (!ctx) return;

  return ChartUtils.createRiasecRadarChart(ctx, riasecData);
}

function displayTopTypes(riasecData) {
  const container = document.getElementById('top-riasec-types');
  if (!container) return;

  // Sort RIASEC scores to get top 3
  const sortedTypes = Object.entries(riasecData)
    .map(([key, value]) => ({
      key,
      value,
      details: RIASEC_DETAILS[key],
      name: RIASEC_DETAILS[key]?.name || key
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  const colors = [
    'bg-yellow-100 border-yellow-300 text-yellow-800',
    'bg-orange-100 border-orange-300 text-orange-800',
    'bg-red-100 border-red-300 text-red-800'
  ];

  container.innerHTML = sortedTypes.map((type, index) => `
    <div class="border-2 rounded-lg p-4 ${colors[index]}">
      <div class="flex justify-between items-center mb-3">
        <h5 class="font-semibold text-lg">#${index + 1} ${type.name}</h5>
        <span class="text-2xl font-bold">${type.value}%</span>
      </div>
      <p class="text-sm mb-3">${type.details?.description || ''}</p>
      <div class="text-xs">
        <strong>Contoh Karir:</strong> ${type.details?.careerExamples?.slice(0, 3).join(', ') || ''}
      </div>
    </div>
  `).join('');
}

function displayDetailedResults(riasecData) {
  const container = document.getElementById('riasec-detailed-results');
  if (!container) return;

  // Sort RIASEC scores to show highest first
  const sortedRiasec = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, details: RIASEC_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedRiasec.map(({ value, details }) => `
    <div class="border rounded-lg p-6 ${value >= 70 ? 'border-blue-300 bg-blue-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
      <div class="flex justify-between items-start mb-4">
        <h4 class="font-semibold text-xl text-gray-900">${details.name}</h4>
        <div class="text-right">
          <div class="text-2xl font-bold ${value >= 70 ? 'text-blue-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
          <div class="text-sm ${value >= 70 ? 'text-blue-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
            ${value >= 70 ? 'Tinggi' : value >= 50 ? 'Sedang' : 'Rendah'}
          </div>
        </div>
      </div>

      <p class="text-gray-700 mb-4">${details.description}</p>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h5 class="font-medium text-gray-800 mb-3">Karakteristik:</h5>
          <ul class="text-gray-600 space-y-2">
            ${details.characteristics.map(char => `<li class="flex items-start"><span class="text-blue-500 mr-2">•</span>${char}</li>`).join('')}
          </ul>
        </div>
        <div>
          <h5 class="font-medium text-gray-800 mb-3">Contoh Karir:</h5>
          <ul class="text-gray-600 space-y-2">
            ${details.careerExamples.map(career => `<li class="flex items-start"><span class="text-green-500 mr-2">•</span>${career}</li>`).join('')}
          </ul>
        </div>
      </div>
    </div>
  `).join('');
}

function displayCareerRecommendations(riasecData) {
  const container = document.getElementById('riasec-career-recommendations');
  if (!container) return;

  // Get top 3 RIASEC types
  const topTypes = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, details: RIASEC_DETAILS[key] }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  container.innerHTML = topTypes.map(({ details, value }) => `
    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <h4 class="font-semibold text-gray-900 mb-3">${details.name} (${value}%)</h4>
      <p class="text-gray-600 text-sm mb-4">${details.description}</p>

      <div class="space-y-3">
        <div>
          <h5 class="font-medium text-gray-800 mb-2">Karir yang Cocok:</h5>
          <div class="flex flex-wrap gap-2">
            ${details.careerExamples.slice(0, 6).map(career =>
              `<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">${career}</span>`
            ).join('')}
          </div>
        </div>

        <div>
          <h5 class="font-medium text-gray-800 mb-2">Lingkungan Kerja:</h5>
          <p class="text-gray-600 text-sm">${details.workEnvironment || 'Lingkungan yang mendukung karakteristik tipe ini'}</p>
        </div>
      </div>
    </div>
  `).join('');
}

function displayWorkEnvironmentPreferences(riasecData) {
  const container = document.getElementById('work-environment-preferences');
  if (!container) return;

  // Get dominant type (highest score)
  const dominantType = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, details: RIASEC_DETAILS[key] }))
    .sort((a, b) => b.value - a.value)[0];

  const workEnvironments = {
    realistic: {
      preferences: [
        'Lingkungan kerja yang praktis dan hands-on',
        'Akses ke alat, mesin, dan teknologi',
        'Tugas yang konkret dan terukur',
        'Minimal birokrasi dan meeting',
        'Hasil kerja yang terlihat nyata'
      ],
      avoid: [
        'Terlalu banyak presentasi dan public speaking',
        'Lingkungan yang terlalu formal',
        'Pekerjaan yang hanya teoritis'
      ]
    },
    investigative: {
      preferences: [
        'Lingkungan yang mendorong penelitian dan analisis',
        'Akses ke data dan informasi',
        'Otonomi dalam bekerja',
        'Tantangan intelektual yang kompleks',
        'Waktu untuk berpikir dan merenungkan'
      ],
      avoid: [
        'Tekanan waktu yang ketat',
        'Terlalu banyak interaksi sosial',
        'Rutinitas yang monoton'
      ]
    },
    artistic: {
      preferences: [
        'Lingkungan yang kreatif dan inspiratif',
        'Fleksibilitas dalam jadwal dan metode kerja',
        'Kebebasan berekspresi',
        'Kolaborasi dengan orang-orang kreatif',
        'Ruang untuk inovasi dan eksperimen'
      ],
      avoid: [
        'Aturan yang terlalu kaku',
        'Lingkungan yang monoton',
        'Tekanan untuk conformity'
      ]
    },
    social: {
      preferences: [
        'Lingkungan yang kolaboratif dan supportif',
        'Interaksi regular dengan orang lain',
        'Kesempatan untuk membantu dan mengajar',
        'Budaya kerja yang humanis',
        'Misi yang bermakna secara sosial'
      ],
      avoid: [
        'Kompetisi yang terlalu agresif',
        'Isolasi atau bekerja sendiri',
        'Lingkungan yang impersonal'
      ]
    },
    enterprising: {
      preferences: [
        'Lingkungan yang dinamis dan kompetitif',
        'Kesempatan untuk memimpin dan mempengaruhi',
        'Target dan goals yang jelas',
        'Reward system yang baik',
        'Networking dan business development'
      ],
      avoid: [
        'Rutinitas yang membosankan',
        'Hierarki yang terlalu kaku',
        'Lingkungan yang terlalu risk-averse'
      ]
    },
    conventional: {
      preferences: [
        'Lingkungan yang terstruktur dan terorganisir',
        'Prosedur dan guidelines yang jelas',
        'Stabilitas dan predictability',
        'Sistem dan tools yang efisien',
        'Hierarki yang jelas'
      ],
      avoid: [
        'Ambiguitas dan ketidakpastian',
        'Perubahan yang terlalu cepat',
        'Lingkungan yang chaotic'
      ]
    }
  };

  const envData = workEnvironments[dominantType.key] || workEnvironments.investigative;

  container.innerHTML = `
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <h4 class="font-semibold text-green-800 mb-4 flex items-center">
          <span class="text-green-500 mr-2">✓</span>
          Lingkungan Kerja yang Ideal
        </h4>
        <p class="text-green-700 text-sm mb-4">
          Berdasarkan tipe dominan Anda (${dominantType.details.name}), berikut adalah preferensi lingkungan kerja:
        </p>
        <ul class="space-y-2">
          ${envData.preferences.map(pref => `
            <li class="flex items-start">
              <span class="text-green-500 mr-2 mt-1">•</span>
              <span class="text-gray-700 text-sm">${pref}</span>
            </li>
          `).join('')}
        </ul>
      </div>

      <div class="bg-red-50 border border-red-200 rounded-lg p-6">
        <h4 class="font-semibold text-red-800 mb-4 flex items-center">
          <span class="text-red-500 mr-2">⚠️</span>
          Lingkungan yang Sebaiknya Dihindari
        </h4>
        <p class="text-red-700 text-sm mb-4">
          Lingkungan kerja yang mungkin tidak cocok dengan preferensi Anda:
        </p>
        <ul class="space-y-2">
          ${envData.avoid.map(avoid => `
            <li class="flex items-start">
              <span class="text-red-500 mr-2 mt-1">•</span>
              <span class="text-gray-700 text-sm">${avoid}</span>
            </li>
          `).join('')}
        </ul>
      </div>
    </div>
  `;
}
