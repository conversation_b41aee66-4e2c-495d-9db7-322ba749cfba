export function createBriefResultPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Dashboard
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Hasil Assessment</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="downloadResult()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Download PDF
              </button>
              <button onclick="shareResult()" class="text-gray-500 hover:text-gray-700">
                Bagikan
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Completion Celebration -->
        <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg shadow p-8 mb-8 text-white text-center">
          <div class="text-6xl mb-4">🎉</div>
          <h2 class="text-3xl font-bold mb-2">Selamat! Assessment Selesai</h2>
          <p class="text-green-100 text-lg">
            Anda telah menyelesaikan assessment psikometri komprehensif. 
            Mari jelajahi hasil dan temukan potensi terbaik Anda!
          </p>
          <div class="mt-4">
            <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
              Diselesaikan pada: <span id="completion-date"></span>
            </span>
          </div>
        </div>

        <!-- Your Persona Preview -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow p-8 mb-8 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-4xl">🧠</span>
            </div>
            <div class="flex-1">
              <h3 class="text-2xl font-bold mb-2" id="archetype">The Analytical Innovator</h3>
              <p class="text-indigo-100 leading-relaxed" id="short-summary">
                Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi.
              </p>
              <div class="mt-4">
                <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  Risk Tolerance: <span id="risk-tolerance" class="font-semibold">Moderate</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Assessment Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6 text-center">Ringkasan Hasil Assessment</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- VIA-IS Preview -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 text-center">
              <div class="text-4xl mb-4">💎</div>
              <h4 class="font-semibold text-purple-800 text-lg mb-2">VIA-IS Character Strengths</h4>
              <p class="text-purple-600 text-sm mb-4">24 kekuatan karakter dalam 6 kategori kebajikan</p>
              <div class="space-y-2" id="via-is-preview">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>

            <!-- RIASEC Preview -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div class="text-4xl mb-4">🎯</div>
              <h4 class="font-semibold text-blue-800 text-lg mb-2">RIASEC Holland Codes</h4>
              <p class="text-blue-600 text-sm mb-4">6 tipe kepribadian kerja dan minat karir</p>
              <div class="space-y-2" id="riasec-preview">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>

            <!-- OCEAN Preview -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
              <div class="text-4xl mb-4">🌊</div>
              <h4 class="font-semibold text-green-800 text-lg mb-2">OCEAN Big Five</h4>
              <p class="text-green-600 text-sm mb-4">5 dimensi kepribadian universal</p>
              <div class="space-y-2" id="ocean-preview">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Journey Progress -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-6 text-center">Perjalanan Eksplorasi Hasil Anda</h3>
          
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-indigo-600 text-white rounded-full flex items-center justify-center font-semibold">
                1
              </div>
              <div>
                <div class="font-medium text-gray-900">Brief Overview</div>
                <div class="text-sm text-gray-500">Ringkasan hasil</div>
              </div>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4"></div>
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center font-semibold">
                2
              </div>
              <div>
                <div class="font-medium text-gray-600">VIA-IS</div>
                <div class="text-sm text-gray-400">Character Strengths</div>
              </div>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4"></div>
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center font-semibold">
                3
              </div>
              <div>
                <div class="font-medium text-gray-600">RIASEC</div>
                <div class="text-sm text-gray-400">Holland Codes</div>
              </div>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4"></div>
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center font-semibold">
                4
              </div>
              <div>
                <div class="font-medium text-gray-600">OCEAN</div>
                <div class="text-sm text-gray-400">Big Five</div>
              </div>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4"></div>
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center font-semibold">
                5
              </div>
              <div>
                <div class="font-medium text-gray-600">Persona</div>
                <div class="text-sm text-gray-400">Profil Lengkap</div>
              </div>
            </div>
          </div>

          <div class="text-center">
            <p class="text-gray-600 mb-6">
              Ikuti perjalanan eksplorasi untuk memahami hasil assessment Anda secara mendalam. 
              Setiap tahap akan memberikan insight berharga untuk pengembangan diri dan karir.
            </p>
            <button onclick="navigateTo('result-via-is')" 
              class="bg-indigo-600 text-white py-3 px-8 rounded-lg hover:bg-indigo-700 transition duration-200 text-lg font-semibold">
              🚀 Mulai Jelajahi Hasil
            </button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow p-6">
            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-yellow-500 mr-2 text-xl">⚡</span>
              Akses Cepat
            </h4>
            <div class="space-y-3">
              <button onclick="navigateTo('result-via-is')" 
                class="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded border border-purple-200 transition duration-200">
                <div class="font-medium text-purple-800">💎 Character Strengths</div>
                <div class="text-sm text-purple-600">Jelajahi 24 kekuatan karakter Anda</div>
              </button>
              <button onclick="navigateTo('result-riasec')" 
                class="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded border border-blue-200 transition duration-200">
                <div class="font-medium text-blue-800">🎯 Minat Karir</div>
                <div class="text-sm text-blue-600">Temukan tipe kepribadian kerja Anda</div>
              </button>
              <button onclick="navigateTo('result-ocean')" 
                class="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded border border-green-200 transition duration-200">
                <div class="font-medium text-green-800">🌊 Kepribadian</div>
                <div class="text-sm text-green-600">Pahami 5 dimensi kepribadian Anda</div>
              </button>
              <button onclick="navigateTo('result-persona')" 
                class="w-full text-left p-3 bg-indigo-50 hover:bg-indigo-100 rounded border border-indigo-200 transition duration-200">
                <div class="font-medium text-indigo-800">🧠 Persona Profile</div>
                <div class="text-sm text-indigo-600">Lihat profil lengkap dan rekomendasi</div>
              </button>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-green-500 mr-2 text-xl">🎯</span>
              Langkah Selanjutnya
            </h4>
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <span class="text-green-500 mt-1">1.</span>
                <div>
                  <div class="font-medium text-gray-800">Jelajahi Hasil Detail</div>
                  <div class="text-sm text-gray-600">Ikuti perjalanan eksplorasi untuk pemahaman mendalam</div>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-green-500 mt-1">2.</span>
                <div>
                  <div class="font-medium text-gray-800">Buat Rencana Pengembangan</div>
                  <div class="text-sm text-gray-600">Gunakan insight untuk merencanakan pengembangan diri</div>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-green-500 mt-1">3.</span>
                <div>
                  <div class="font-medium text-gray-800">Konsultasi dengan Expert</div>
                  <div class="text-sm text-gray-600">Diskusikan hasil dengan konselor karir</div>
                </div>
              </div>
            </div>
            <button onclick="scheduleConsultation()" 
              class="w-full mt-4 bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition duration-200">
              Jadwalkan Konsultasi
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button onclick="retakeAssessment()" 
            class="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            Ulangi Assessment
          </button>
          <button onclick="navigateTo('result-via-is')" 
            class="flex-1 bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            🚀 Mulai Eksplorasi
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initBriefResult() {
  // Set completion date
  const completionDate = new Date().toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const dateElement = document.getElementById('completion-date');
  if (dateElement) {
    dateElement.textContent = completionDate;
  }

  // Load and display brief results
  loadBriefResults();
}

function loadBriefResults() {
  try {
    // Load persona profile
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : getPersonaProfileExample();

    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleAssessmentResults();

    console.log('Brief results loaded:', { profile, assessmentResults });

    displayBriefResults(profile, assessmentResults);
  } catch (error) {
    console.error('Error loading brief results:', error);
    // Fallback to example data
    displayBriefResults(getPersonaProfileExample(), getSampleAssessmentResults());
  }
}

function displayBriefResults(profile, assessmentResults) {
  // Update persona preview
  const archetypeElement = document.getElementById('archetype');
  const shortSummaryElement = document.getElementById('short-summary');
  const riskToleranceElement = document.getElementById('risk-tolerance');

  if (archetypeElement) archetypeElement.textContent = profile.archetype;
  if (shortSummaryElement) shortSummaryElement.textContent = profile.shortSummary;
  if (riskToleranceElement) riskToleranceElement.textContent = profile.riskTolerance;

  // Display VIA-IS preview
  displayViaIsPreview(assessmentResults.viaIs);

  // Display RIASEC preview
  displayRiasecPreview(assessmentResults.riasec);

  // Display OCEAN preview
  displayOceanPreview(assessmentResults.ocean);
}

function displayViaIsPreview(viaIsData) {
  const container = document.getElementById('via-is-preview');
  if (!container) return;

  // Get top 3 strengths
  const topStrengths = Object.entries(viaIsData)
    .map(([key, value]) => ({ key, value, name: getViaIsDisplayName(key) }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  container.innerHTML = topStrengths.map((strength, index) => `
    <div class="flex justify-between items-center text-sm">
      <span class="text-purple-700">#${index + 1} ${strength.name}</span>
      <span class="font-semibold text-purple-800">${strength.value}%</span>
    </div>
  `).join('');
}

function displayRiasecPreview(riasecData) {
  const container = document.getElementById('riasec-preview');
  if (!container) return;

  // Get top 3 types
  const topTypes = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, name: getRiasecDisplayName(key) }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  container.innerHTML = topTypes.map((type, index) => `
    <div class="flex justify-between items-center text-sm">
      <span class="text-blue-700">#${index + 1} ${type.name}</span>
      <span class="font-semibold text-blue-800">${type.value}%</span>
    </div>
  `).join('');
}

function displayOceanPreview(oceanData) {
  const container = document.getElementById('ocean-preview');
  if (!container) return;

  // Get top 3 dimensions
  const topDimensions = Object.entries(oceanData)
    .map(([key, value]) => ({ key, value, name: getOceanDisplayName(key) }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  container.innerHTML = topDimensions.map((dimension, index) => `
    <div class="flex justify-between items-center text-sm">
      <span class="text-green-700">#${index + 1} ${dimension.name}</span>
      <span class="font-semibold text-green-800">${dimension.value}%</span>
    </div>
  `).join('');
}

function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Kreativitas',
    curiosity: 'Keingintahuan',
    judgment: 'Penilaian',
    loveOfLearning: 'Cinta Belajar',
    perspective: 'Perspektif',
    bravery: 'Keberanian',
    perseverance: 'Ketekunan',
    honesty: 'Kejujuran',
    zest: 'Semangat',
    love: 'Cinta',
    kindness: 'Kebaikan',
    socialIntelligence: 'Kecerdasan Sosial',
    teamwork: 'Kerja Tim',
    fairness: 'Keadilan',
    leadership: 'Kepemimpinan',
    forgiveness: 'Pengampunan',
    humility: 'Kerendahan Hati',
    prudence: 'Kehati-hatian',
    selfRegulation: 'Pengaturan Diri',
    appreciationOfBeauty: 'Apresiasi Keindahan',
    gratitude: 'Rasa Syukur',
    hope: 'Harapan',
    humor: 'Humor',
    spirituality: 'Spiritualitas'
  };
  return displayNames[key] || key;
}

function getRiasecDisplayName(key) {
  const displayNames = {
    realistic: 'Realistic',
    investigative: 'Investigative',
    artistic: 'Artistic',
    social: 'Social',
    enterprising: 'Enterprising',
    conventional: 'Conventional'
  };
  return displayNames[key] || key;
}

function getOceanDisplayName(key) {
  const displayNames = {
    openness: 'Openness',
    conscientiousness: 'Conscientiousness',
    extraversion: 'Extraversion',
    agreeableness: 'Agreeableness',
    neuroticism: 'Neuroticism'
  };
  return displayNames[key] || key;
}

function getPersonaProfileExample() {
  return {
    archetype: "The Analytical Innovator",
    shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
    riskTolerance: "moderate"
  };
}

function getSampleAssessmentResults() {
  return {
    ocean: {
      openness: 85,
      conscientiousness: 78,
      extraversion: 45,
      agreeableness: 62,
      neuroticism: 35
    },
    riasec: {
      realistic: 25,
      investigative: 92,
      artistic: 78,
      social: 45,
      enterprising: 55,
      conventional: 68
    },
    viaIs: {
      creativity: 88,
      curiosity: 92,
      judgment: 85,
      loveOfLearning: 90,
      perspective: 82,
      bravery: 65,
      perseverance: 78,
      honesty: 75,
      zest: 58,
      love: 62,
      kindness: 68,
      socialIntelligence: 55,
      teamwork: 48,
      fairness: 72,
      leadership: 65,
      forgiveness: 58,
      humility: 62,
      prudence: 85,
      selfRegulation: 75,
      appreciationOfBeauty: 82,
      gratitude: 68,
      hope: 72,
      humor: 55,
      spirituality: 45
    }
  };
}

// Export functions that will be used globally
export function downloadResult() {
  // Simulate PDF download
  alert('Fitur download PDF akan segera tersedia');
}

export function shareResult() {
  // Simulate sharing functionality
  if (navigator.share) {
    navigator.share({
      title: 'Hasil Assessment Talenta',
      text: 'Lihat hasil assessment talenta saya',
      url: window.location.href
    });
  } else {
    // Fallback for browsers that don't support Web Share API
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      alert('Link hasil assessment telah disalin ke clipboard');
    });
  }
}

export function retakeAssessment() {
  if (confirm('Apakah Anda yakin ingin mengulang assessment? Hasil sebelumnya akan ditimpa.')) {
    // Clear all assessment data including persona profile
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('assessmentCompleted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastSaveResult');
    localStorage.removeItem('lastAssessmentId');
    localStorage.removeItem('personaProfile');

    // Navigate to assessment
    navigateTo('assessment');
  }
}

export function scheduleConsultation() {
  // In a real application, this would open a consultation booking form
  const personaProfile = JSON.parse(localStorage.getItem('personaProfile') || '{}');
  const archetype = personaProfile.archetype || 'Unknown';

  alert(`Fitur penjadwalan konsultasi akan segera tersedia.\n\nBerdasarkan profil "${archetype}", kami akan merekomendasikan konselor yang tepat untuk Anda.`);
}
