export function createResultPersonaPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('result-ocean')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← OCEAN
              </button>
              <h1 class="text-xl font-semibold text-gray-900">Persona Profile</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="navigateTo('result')" class="text-indigo-600 hover:text-indigo-700">
                <PERSON><PERSON> →
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow p-8 mb-8 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-4xl">🧠</span>
            </div>
            <div class="flex-1">
              <h2 class="text-3xl font-bold mb-2" id="archetype">The Analytical Innovator</h2>
              <p class="text-indigo-100 leading-relaxed" id="short-summary">
                Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi.
              </p>
              <div class="mt-4">
                <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  Risk Tolerance: <span id="risk-tolerance" class="font-semibold">Moderate</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Strengths and Weaknesses -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-green-500 mr-3 text-2xl">💪</span>
              Kekuatan Utama
            </h3>
            <p class="text-gray-600 mb-4" id="strength-summary">
              Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru.
            </p>
            <div class="space-y-3" id="strengths-list">
              <!-- Strengths will be populated by JavaScript -->
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-orange-500 mr-3 text-2xl">🎯</span>
              Area Pengembangan
            </h3>
            <p class="text-gray-600 mb-4" id="weakness-summary">
              Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat.
            </p>
            <div class="space-y-3" id="weaknesses-list">
              <!-- Weaknesses will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Career Recommendations -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <span class="text-blue-500 mr-3 text-2xl">🚀</span>
            Rekomendasi Karir
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="career-recommendations">
            <!-- Career recommendations will be populated by JavaScript -->
          </div>
        </div>

        <!-- Work Environment -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <span class="text-purple-500 mr-3 text-2xl">🏢</span>
            Lingkungan Kerja Ideal
          </h3>
          <p class="text-gray-600 leading-relaxed text-lg" id="work-environment">
            Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan.
          </p>
        </div>

        <!-- Insights and Skills -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-yellow-500 mr-3 text-2xl">💡</span>
              Insights & Saran
            </h3>
            <div class="space-y-3" id="insights-list">
              <!-- Insights will be populated by JavaScript -->
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
              <span class="text-indigo-500 mr-3 text-2xl">🎓</span>
              Skill yang Disarankan
            </h3>
            <div class="flex flex-wrap gap-3" id="skill-suggestions">
              <!-- Skills will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Potential Pitfalls -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <h3 class="text-xl font-semibold text-red-800 mb-4 flex items-center">
            <span class="text-red-500 mr-3 text-2xl">⚠️</span>
            Potensi Jebakan yang Perlu Diwaspadai
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="pitfalls-list">
            <!-- Pitfalls will be populated by JavaScript -->
          </div>
        </div>

        <!-- Role Models -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <span class="text-amber-500 mr-3 text-2xl">⭐</span>
            Role Model yang Menginspirasi
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="role-models">
            <!-- Role models will be populated by JavaScript -->
          </div>
        </div>

        <!-- Assessment Summary -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <span class="text-teal-500 mr-3 text-2xl">📊</span>
            Ringkasan Assessment
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-purple-50 rounded-lg">
              <div class="text-3xl mb-2">💎</div>
              <h4 class="font-semibold text-purple-800">VIA-IS</h4>
              <p class="text-purple-600 text-sm">Character Strengths</p>
              <button onclick="navigateTo('result-via-is')" class="mt-2 text-purple-600 hover:text-purple-800 text-sm underline">
                Lihat Detail →
              </button>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-3xl mb-2">🎯</div>
              <h4 class="font-semibold text-blue-800">RIASEC</h4>
              <p class="text-blue-600 text-sm">Holland Codes</p>
              <button onclick="navigateTo('result-riasec')" class="mt-2 text-blue-600 hover:text-blue-800 text-sm underline">
                Lihat Detail →
              </button>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-3xl mb-2">🌊</div>
              <h4 class="font-semibold text-green-800">OCEAN</h4>
              <p class="text-green-600 text-sm">Big Five Personality</p>
              <button onclick="navigateTo('result-ocean')" class="mt-2 text-green-600 hover:text-green-800 text-sm underline">
                Lihat Detail →
              </button>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mb-8">
          <button onclick="retakeAssessment()" 
            class="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            Ulangi Assessment
          </button>
          <button onclick="scheduleConsultation()" 
            class="flex-1 bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Jadwalkan Konsultasi
          </button>
          <button onclick="downloadResult()" 
            class="flex-1 bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 transition duration-200">
            Download PDF
          </button>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
          <button onclick="navigateTo('result-ocean')" 
            class="bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            ← Kembali ke OCEAN
          </button>
          <button onclick="navigateTo('result')" 
            class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Lihat Hasil Lengkap →
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultPersona() {
  // Load persona profile data
  loadPersonaProfile();
}

function loadPersonaProfile() {
  try {
    // Try to load from localStorage first, fallback to example data
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : getPersonaProfileExample();

    console.log('Persona profile loaded:', profile);

    displayPersonaProfile(profile);
  } catch (error) {
    console.error('Error loading persona profile:', error);
    // Fallback to example data
    displayPersonaProfile(getPersonaProfileExample());
  }
}

function getPersonaProfileExample() {
  return {
    archetype: "The Analytical Innovator",
    shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
    strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
    strengths: [
      "Kemampuan analisis yang tajam",
      "Kreativitas dan inovasi",
      "Keingintahuan intelektual yang tinggi",
      "Kemampuan belajar mandiri yang kuat",
      "Pemikiran sistematis dan terstruktur"
    ],
    weaknessSummary: "Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat atau bekerja sama dengan orang lain.",
    weaknesses: [
      "Terkadang terlalu perfeksionis",
      "Dapat terjebak dalam overthinking",
      "Kurang sabar dengan proses yang lambat",
      "Kemampuan sosial yang perlu dikembangkan",
      "Kesulitan mendelegasikan tugas"
    ],
    careerRecommendation: [
      {
        careerName: "Data Scientist",
        careerProspect: {
          jobAvailability: "high",
          salaryPotential: "high",
          careerProgression: "high",
          industryGrowth: "super high",
          skillDevelopment: "super high"
        }
      },
      {
        careerName: "Peneliti",
        careerProspect: {
          jobAvailability: "moderate",
          salaryPotential: "moderate",
          careerProgression: "moderate",
          industryGrowth: "moderate",
          skillDevelopment: "high"
        }
      },
      {
        careerName: "Pengembang Software",
        careerProspect: {
          jobAvailability: "super high",
          salaryPotential: "high",
          careerProgression: "high",
          industryGrowth: "super high",
          skillDevelopment: "super high"
        }
      }
    ],
    insights: [
      "Kembangkan keterampilan komunikasi untuk menyampaikan ide kompleks dengan lebih efektif",
      "Latih kemampuan bekerja dalam tim untuk mengimbangi kecenderungan bekerja sendiri",
      "Manfaatkan kekuatan analitis untuk memecahkan masalah sosial",
      "Cari mentor yang dapat membantu mengembangkan keterampilan kepemimpinan",
      "Tetapkan batas waktu untuk menghindari analisis berlebihan"
    ],
    skillSuggestion: [
      "Public Speaking",
      "Leadership",
      "Teamwork",
      "Time Management",
      "Delegation"
    ],
    possiblePitfalls: [
      "Mengisolasi diri dari tim karena terlalu fokus pada analisis individu",
      "Menunda keputusan karena perfeksionisme berlebihan",
      "Kurang membangun jaringan karena terlalu fokus pada teknis"
    ],
    riskTolerance: "moderate",
    workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
    roleModel: [
      "Marie Curie",
      "Albert Einstein",
      "B.J. Habibie"
    ]
  };
}

function displayPersonaProfile(profile) {
  // Update archetype and summary
  const archetypeElement = document.getElementById('archetype');
  const shortSummaryElement = document.getElementById('short-summary');
  const riskToleranceElement = document.getElementById('risk-tolerance');

  if (archetypeElement) archetypeElement.textContent = profile.archetype;
  if (shortSummaryElement) shortSummaryElement.textContent = profile.shortSummary;
  if (riskToleranceElement) riskToleranceElement.textContent = profile.riskTolerance;

  // Update strength summary and list
  const strengthSummaryElement = document.getElementById('strength-summary');
  const strengthsListElement = document.getElementById('strengths-list');

  if (strengthSummaryElement) strengthSummaryElement.textContent = profile.strengthSummary;
  if (strengthsListElement) {
    strengthsListElement.innerHTML = profile.strengths.map(strength =>
      `<div class="flex items-start space-x-3">
        <span class="text-green-500 mt-1 text-lg">✓</span>
        <span class="text-gray-700">${strength}</span>
      </div>`
    ).join('');
  }

  // Update weakness summary and list
  const weaknessSummaryElement = document.getElementById('weakness-summary');
  const weaknessesListElement = document.getElementById('weaknesses-list');

  if (weaknessSummaryElement) weaknessSummaryElement.textContent = profile.weaknessSummary;
  if (weaknessesListElement) {
    weaknessesListElement.innerHTML = profile.weaknesses.map(weakness =>
      `<div class="flex items-start space-x-3">
        <span class="text-orange-500 mt-1 text-lg">•</span>
        <span class="text-gray-700">${weakness}</span>
      </div>`
    ).join('');
  }

  // Update career recommendations
  displayCareerRecommendations(profile.careerRecommendation);

  // Update work environment
  const workEnvironmentElement = document.getElementById('work-environment');
  if (workEnvironmentElement) workEnvironmentElement.textContent = profile.workEnvironment;

  // Update insights
  const insightsListElement = document.getElementById('insights-list');
  if (insightsListElement) {
    insightsListElement.innerHTML = profile.insights.map(insight =>
      `<div class="flex items-start space-x-3">
        <span class="text-yellow-500 mt-1 text-lg">💡</span>
        <span class="text-gray-700">${insight}</span>
      </div>`
    ).join('');
  }

  // Update skill suggestions
  const skillSuggestionsElement = document.getElementById('skill-suggestions');
  if (skillSuggestionsElement) {
    skillSuggestionsElement.innerHTML = profile.skillSuggestion.map(skill =>
      `<span class="bg-indigo-100 text-indigo-800 px-4 py-2 rounded-full font-medium">${skill}</span>`
    ).join('');
  }

  // Update possible pitfalls
  const pitfallsListElement = document.getElementById('pitfalls-list');
  if (pitfallsListElement) {
    pitfallsListElement.innerHTML = profile.possiblePitfalls.map(pitfall =>
      `<div class="flex items-start space-x-3 p-3 bg-white rounded border border-red-100">
        <span class="text-red-500 mt-1 text-lg">⚠️</span>
        <span class="text-red-700">${pitfall}</span>
      </div>`
    ).join('');
  }

  // Update role models
  const roleModelsElement = document.getElementById('role-models');
  if (roleModelsElement) {
    roleModelsElement.innerHTML = profile.roleModel.map(model =>
      `<div class="bg-amber-50 border border-amber-200 rounded-lg p-4 text-center">
        <div class="text-2xl mb-2">⭐</div>
        <span class="text-amber-800 font-semibold">${model}</span>
      </div>`
    ).join('');
  }
}

function displayCareerRecommendations(careerRecommendations) {
  const careerRecommendationsElement = document.getElementById('career-recommendations');
  if (!careerRecommendationsElement) return;

  const getProspectColor = (level) => {
    switch (level) {
      case 'super high': return 'bg-green-500';
      case 'high': return 'bg-blue-500';
      case 'moderate': return 'bg-yellow-500';
      case 'low': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getProspectText = (level) => {
    switch (level) {
      case 'super high': return 'Sangat Tinggi';
      case 'high': return 'Tinggi';
      case 'moderate': return 'Sedang';
      case 'low': return 'Rendah';
      default: return level;
    }
  };

  careerRecommendationsElement.innerHTML = careerRecommendations.map(career => {
    const prospects = career.careerProspect;
    const avgScore = Object.values(prospects).reduce((sum, val) => {
      const score = val === 'super high' ? 5 : val === 'high' ? 4 : val === 'moderate' ? 3 : val === 'low' ? 2 : 1;
      return sum + score;
    }, 0) / Object.keys(prospects).length;

    return `
      <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
        <h4 class="font-semibold text-gray-900 mb-4 text-lg">${career.careerName}</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Ketersediaan Kerja</span>
            <span class="text-xs px-3 py-1 rounded-full text-white ${getProspectColor(prospects.jobAvailability)}">
              ${getProspectText(prospects.jobAvailability)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Potensi Gaji</span>
            <span class="text-xs px-3 py-1 rounded-full text-white ${getProspectColor(prospects.salaryPotential)}">
              ${getProspectText(prospects.salaryPotential)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Perkembangan Karir</span>
            <span class="text-xs px-3 py-1 rounded-full text-white ${getProspectColor(prospects.careerProgression)}">
              ${getProspectText(prospects.careerProgression)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Pertumbuhan Industri</span>
            <span class="text-xs px-3 py-1 rounded-full text-white ${getProspectColor(prospects.industryGrowth)}">
              ${getProspectText(prospects.industryGrowth)}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Pengembangan Skill</span>
            <span class="text-xs px-3 py-1 rounded-full text-white ${getProspectColor(prospects.skillDevelopment)}">
              ${getProspectText(prospects.skillDevelopment)}
            </span>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-100">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Skor Keseluruhan</span>
            <span class="text-lg font-bold text-indigo-600">${(avgScore * 20).toFixed(0)}%</span>
          </div>
        </div>
      </div>
    `;
  }).join('');
}
