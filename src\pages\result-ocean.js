// Import utilities for OCEAN assessment details and charts
import { OCEAN_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultOceanPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('result-riasec')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← RIASEC
              </button>
              <h1 class="text-xl font-semibold text-gray-900">OCEAN Big Five Personality</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="navigateTo('result-persona')" class="text-indigo-600 hover:text-indigo-700">
                Persona Profile →
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg shadow p-8 mb-8 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-4xl">🌊</span>
            </div>
            <div class="flex-1">
              <h2 class="text-3xl font-bold mb-2">OCEAN Big Five Personality</h2>
              <p class="text-green-100 text-lg leading-relaxed">
                Model Big Five mengukur 5 dimensi utama kepribadian yang stabil dan universal dalam menjelaskan perbedaan individual.
              </p>
            </div>
          </div>
        </div>

        <!-- OCEAN Explanation -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Tentang OCEAN Big Five Personality</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Apa itu Big Five?</h4>
              <div class="space-y-4 text-gray-700">
                <p>
                  Model Big Five atau OCEAN adalah model kepribadian yang paling diterima secara ilmiah 
                  dan digunakan luas dalam psikologi kepribadian.
                </p>
                <p>
                  Model ini mengidentifikasi 5 dimensi utama kepribadian yang relatif stabil sepanjang hidup 
                  dan dapat memprediksi berbagai aspek perilaku, kinerja, dan kesejahteraan.
                </p>
                <p>
                  OCEAN merupakan singkatan dari: <strong>O</strong>penness, <strong>C</strong>onscientiousness, 
                  <strong>E</strong>xtraversion, <strong>A</strong>greeableness, dan <strong>N</strong>euroticism.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Manfaat Memahami Big Five</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Pemahaman diri yang mendalam dan akurat</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Prediksi kinerja kerja dan akademik</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan hubungan interpersonal</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Panduan untuk pengembangan diri</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-green-500 mt-1">✓</span>
                  <span class="text-gray-700">Memahami gaya komunikasi dan kerja</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- OCEAN Dimensions Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">5 Dimensi Kepribadian OCEAN</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="ocean-dimensions-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- User's OCEAN Profile -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Profil OCEAN Anda</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Radar Chart -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Visualisasi Radar Chart</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="oceanRadarChart" width="400" height="400"></canvas>
              </div>
            </div>

            <!-- Personality Summary -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Ringkasan Kepribadian</h4>
              <div id="personality-summary" class="space-y-4">
                <!-- Will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed OCEAN Results -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Detail Hasil Assessment OCEAN</h3>
          <div id="ocean-detailed-results" class="space-y-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Behavioral Implications -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Implikasi Perilaku dan Kinerja</h3>
          <div id="behavioral-implications" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Development Recommendations -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Rekomendasi Pengembangan</h3>
          <div id="development-recommendations" class="space-y-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
          <button onclick="navigateTo('result-riasec')" 
            class="bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            ← Kembali ke RIASEC
          </button>
          <button onclick="navigateTo('result-persona')" 
            class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Lanjut ke Persona Profile →
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultOcean() {
  // Load OCEAN assessment data
  loadOceanData();
}

function loadOceanData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleOceanData();

    console.log('OCEAN assessment results loaded:', assessmentResults);

    displayOceanData(assessmentResults.ocean || assessmentResults);
  } catch (error) {
    console.error('Error loading OCEAN data:', error);
    // Fallback to sample data
    displayOceanData(getSampleOceanData());
  }
}

function getSampleOceanData() {
  return {
    openness: 85,
    conscientiousness: 78,
    extraversion: 45,
    agreeableness: 62,
    neuroticism: 35
  };
}

function displayOceanData(oceanData) {
  // Display dimensions overview
  displayDimensionsOverview();

  // Create radar chart
  createOceanRadarChart(oceanData);

  // Display personality summary
  displayPersonalitySummary(oceanData);

  // Display detailed results
  displayDetailedResults(oceanData);

  // Display behavioral implications
  displayBehavioralImplications(oceanData);

  // Display development recommendations
  displayDevelopmentRecommendations(oceanData);
}

function displayDimensionsOverview() {
  const container = document.getElementById('ocean-dimensions-overview');
  if (!container) return;

  const oceanDimensions = [
    {
      code: 'O',
      name: 'Openness',
      fullName: 'Openness to Experience',
      description: 'Keterbukaan terhadap pengalaman baru, kreativitas, dan ide-ide abstrak',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      icon: '🎨'
    },
    {
      code: 'C',
      name: 'Conscientiousness',
      fullName: 'Conscientiousness',
      description: 'Kedisiplinan, keteraturan, dan orientasi pada tujuan',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: '📋'
    },
    {
      code: 'E',
      name: 'Extraversion',
      fullName: 'Extraversion',
      description: 'Energi yang diarahkan keluar, sosiabilitas, dan assertiveness',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: '🎉'
    },
    {
      code: 'A',
      name: 'Agreeableness',
      fullName: 'Agreeableness',
      description: 'Kecenderungan untuk kooperatif, empati, dan kepercayaan',
      color: 'bg-green-50 border-green-200 text-green-800',
      icon: '🤝'
    },
    {
      code: 'N',
      name: 'Neuroticism',
      fullName: 'Neuroticism',
      description: 'Kecenderungan mengalami emosi negatif dan ketidakstabilan emosional',
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: '😰'
    }
  ];

  container.innerHTML = oceanDimensions.map(dimension => `
    <div class="border rounded-lg p-6 ${dimension.color}">
      <div class="flex items-center mb-4">
        <span class="text-3xl mr-4">${dimension.icon}</span>
        <div>
          <h4 class="font-semibold text-lg">${dimension.code} - ${dimension.name}</h4>
          <p class="text-xs opacity-75">${dimension.fullName}</p>
        </div>
      </div>
      <p class="text-sm">${dimension.description}</p>
    </div>
  `).join('');
}

function createOceanRadarChart(oceanData) {
  const ctx = document.getElementById('oceanRadarChart');
  if (!ctx) return;

  return ChartUtils.createOceanRadarChart(ctx, oceanData);
}

function displayPersonalitySummary(oceanData) {
  const container = document.getElementById('personality-summary');
  if (!container) return;

  // Sort dimensions by score
  const sortedDimensions = Object.entries(oceanData)
    .map(([key, value]) => ({
      key,
      value,
      details: OCEAN_DETAILS[key],
      name: OCEAN_DETAILS[key]?.name || key
    }))
    .sort((a, b) => b.value - a.value);

  const highest = sortedDimensions[0];
  const lowest = sortedDimensions[sortedDimensions.length - 1];

  container.innerHTML = `
    <div class="space-y-4">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h5 class="font-semibold text-green-800 mb-2">Dimensi Tertinggi</h5>
        <div class="flex justify-between items-center">
          <span class="text-green-700">${highest.name}</span>
          <span class="text-green-600 font-bold text-lg">${highest.value}%</span>
        </div>
        <p class="text-green-600 text-sm mt-2">${highest.details?.description || ''}</p>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h5 class="font-semibold text-orange-800 mb-2">Dimensi Terendah</h5>
        <div class="flex justify-between items-center">
          <span class="text-orange-700">${lowest.name}</span>
          <span class="text-orange-600 font-bold text-lg">${lowest.value}%</span>
        </div>
        <p class="text-orange-600 text-sm mt-2">${lowest.details?.description || ''}</p>
      </div>

      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 class="font-semibold text-blue-800 mb-2">Profil Kepribadian</h5>
        <div class="space-y-2">
          ${sortedDimensions.map(dim => `
            <div class="flex justify-between items-center">
              <span class="text-blue-700 text-sm">${dim.name}</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-blue-200 rounded-full h-2">
                  <div class="bg-blue-500 h-2 rounded-full" style="width: ${dim.value}%"></div>
                </div>
                <span class="text-blue-600 font-medium text-sm">${dim.value}%</span>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
}

function displayDetailedResults(oceanData) {
  const container = document.getElementById('ocean-detailed-results');
  if (!container) return;

  // Sort OCEAN scores to show highest first
  const sortedOcean = Object.entries(oceanData)
    .map(([key, value]) => ({ key, value, details: OCEAN_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedOcean.map(({ value, details }) => {
    const isHigh = value >= 60;
    const scoreData = isHigh ? details.highScore : details.lowScore;

    return `
      <div class="border rounded-lg p-6 ${value >= 70 ? 'border-green-300 bg-green-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
        <div class="flex justify-between items-start mb-4">
          <h4 class="font-semibold text-xl text-gray-900">${details.name}</h4>
          <div class="text-right">
            <div class="text-2xl font-bold ${value >= 70 ? 'text-green-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
            <div class="text-sm ${value >= 70 ? 'text-green-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
              ${value >= 60 ? 'Tinggi' : value >= 40 ? 'Sedang' : 'Rendah'}
            </div>
          </div>
        </div>

        <p class="text-gray-700 mb-4">${details.description}</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 class="font-medium text-gray-800 mb-3">Karakteristik (${isHigh ? 'Skor Tinggi' : 'Skor Rendah'}):</h5>
            <ul class="text-gray-600 space-y-2">
              ${scoreData.characteristics.map(char => `<li class="flex items-start"><span class="text-green-500 mr-2">•</span>${char}</li>`).join('')}
            </ul>
          </div>
          <div>
            <h5 class="font-medium text-gray-800 mb-3">Implikasi:</h5>
            <ul class="text-gray-600 space-y-2">
              ${scoreData.implications.map(impl => `<li class="flex items-start"><span class="text-blue-500 mr-2">•</span>${impl}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }).join('');
}

function displayBehavioralImplications(oceanData) {
  const container = document.getElementById('behavioral-implications');
  if (!container) return;

  // Get behavioral implications based on scores
  const implications = getBehavioralImplications(oceanData);

  container.innerHTML = `
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h4 class="font-semibold text-blue-800 mb-4 flex items-center">
        <span class="text-blue-500 mr-2">💼</span>
        Implikasi di Tempat Kerja
      </h4>
      <ul class="space-y-2">
        ${implications.work.map(impl => `
          <li class="flex items-start">
            <span class="text-blue-500 mr-2 mt-1">•</span>
            <span class="text-gray-700 text-sm">${impl}</span>
          </li>
        `).join('')}
      </ul>
    </div>

    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
      <h4 class="font-semibold text-green-800 mb-4 flex items-center">
        <span class="text-green-500 mr-2">👥</span>
        Implikasi dalam Hubungan
      </h4>
      <ul class="space-y-2">
        ${implications.relationships.map(impl => `
          <li class="flex items-start">
            <span class="text-green-500 mr-2 mt-1">•</span>
            <span class="text-gray-700 text-sm">${impl}</span>
          </li>
        `).join('')}
      </ul>
    </div>
  `;
}

function displayDevelopmentRecommendations(oceanData) {
  const container = document.getElementById('development-recommendations');
  if (!container) return;

  // Get development recommendations based on scores
  const recommendations = getDevelopmentRecommendations(oceanData);

  container.innerHTML = `
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h4 class="font-semibold text-yellow-800 mb-4 flex items-center">
          <span class="text-yellow-500 mr-2">💪</span>
          Kekuatan yang Dapat Dimanfaatkan
        </h4>
        <ul class="space-y-2">
          ${recommendations.strengths.map(strength => `
            <li class="flex items-start">
              <span class="text-yellow-500 mr-2 mt-1">•</span>
              <span class="text-gray-700 text-sm">${strength}</span>
            </li>
          `).join('')}
        </ul>
      </div>

      <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <h4 class="font-semibold text-purple-800 mb-4 flex items-center">
          <span class="text-purple-500 mr-2">🎯</span>
          Area Pengembangan
        </h4>
        <ul class="space-y-2">
          ${recommendations.development.map(dev => `
            <li class="flex items-start">
              <span class="text-purple-500 mr-2 mt-1">•</span>
              <span class="text-gray-700 text-sm">${dev}</span>
            </li>
          `).join('')}
        </ul>
      </div>
    </div>

    <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mt-6">
      <h4 class="font-semibold text-indigo-800 mb-4 flex items-center">
        <span class="text-indigo-500 mr-2">📚</span>
        Saran Praktis untuk Pengembangan
      </h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        ${recommendations.practical.map(practical => `
          <div class="bg-white rounded p-3">
            <h5 class="font-medium text-indigo-700 mb-2">${practical.area}</h5>
            <p class="text-gray-600 text-sm">${practical.suggestion}</p>
          </div>
        `).join('')}
      </div>
    </div>
  `;
}

function getBehavioralImplications(oceanData) {
  const implications = {
    work: [],
    relationships: []
  };

  // Openness implications
  if (oceanData.openness >= 60) {
    implications.work.push('Cenderung inovatif dan terbuka terhadap perubahan');
    implications.relationships.push('Menikmati diskusi tentang ide-ide abstrak dan filosofis');
  } else {
    implications.work.push('Lebih menyukai rutinitas dan prosedur yang sudah terbukti');
    implications.relationships.push('Lebih praktis dalam pendekatan terhadap masalah');
  }

  // Conscientiousness implications
  if (oceanData.conscientiousness >= 60) {
    implications.work.push('Sangat dapat diandalkan dan memiliki etos kerja yang tinggi');
    implications.relationships.push('Dapat dipercaya dan konsisten dalam komitmen');
  } else {
    implications.work.push('Mungkin memerlukan struktur eksternal untuk tetap fokus');
    implications.relationships.push('Lebih spontan dan fleksibel dalam rencana');
  }

  // Extraversion implications
  if (oceanData.extraversion >= 60) {
    implications.work.push('Berkembang dalam lingkungan tim dan presentasi');
    implications.relationships.push('Mudah bersosialisasi dan membangun jaringan');
  } else {
    implications.work.push('Lebih produktif dalam pekerjaan yang memerlukan konsentrasi mendalam');
    implications.relationships.push('Lebih selektif dalam memilih teman dekat');
  }

  // Agreeableness implications
  if (oceanData.agreeableness >= 60) {
    implications.work.push('Excellent dalam kerja tim dan resolusi konflik');
    implications.relationships.push('Sangat empati dan supportif terhadap orang lain');
  } else {
    implications.work.push('Lebih objektif dalam pengambilan keputusan bisnis');
    implications.relationships.push('Lebih langsung dalam komunikasi dan feedback');
  }

  // Neuroticism implications
  if (oceanData.neuroticism >= 60) {
    implications.work.push('Mungkin memerlukan dukungan tambahan dalam situasi stres');
    implications.relationships.push('Lebih sensitif terhadap kritik dan konflik');
  } else {
    implications.work.push('Tetap tenang dan stabil dalam situasi tekanan tinggi');
    implications.relationships.push('Memberikan stabilitas emosional dalam hubungan');
  }

  return implications;
}

function getDevelopmentRecommendations(oceanData) {
  const recommendations = {
    strengths: [],
    development: [],
    practical: []
  };

  // Identify strengths (scores >= 70)
  Object.entries(oceanData).forEach(([key, value]) => {
    if (value >= 70) {
      const strengthMap = {
        openness: 'Kreativitas dan keterbukaan terhadap pengalaman baru',
        conscientiousness: 'Kedisiplinan dan orientasi pada tujuan yang kuat',
        extraversion: 'Energi sosial dan kemampuan komunikasi yang baik',
        agreeableness: 'Empati dan kemampuan bekerja sama yang excellent',
        neuroticism: 'Sensitivitas tinggi yang dapat menjadi kekuatan dalam memahami emosi'
      };
      if (strengthMap[key]) {
        recommendations.strengths.push(strengthMap[key]);
      }
    }
  });

  // Identify development areas (scores <= 40)
  Object.entries(oceanData).forEach(([key, value]) => {
    if (value <= 40) {
      const developmentMap = {
        openness: 'Mengembangkan keterbukaan terhadap ide dan pengalaman baru',
        conscientiousness: 'Meningkatkan kedisiplinan dan manajemen waktu',
        extraversion: 'Mengembangkan keterampilan sosial dan networking',
        agreeableness: 'Meningkatkan empati dan keterampilan interpersonal',
        neuroticism: 'Mengembangkan strategi manajemen stres dan stabilitas emosional'
      };
      if (developmentMap[key]) {
        recommendations.development.push(developmentMap[key]);
      }
    }
  });

  // Practical suggestions based on profile
  if (oceanData.openness < 50) {
    recommendations.practical.push({
      area: 'Kreativitas',
      suggestion: 'Coba aktivitas baru setiap minggu, baca buku dari genre berbeda, atau ikuti workshop kreatif'
    });
  }

  if (oceanData.conscientiousness < 50) {
    recommendations.practical.push({
      area: 'Organisasi',
      suggestion: 'Gunakan aplikasi task management, buat jadwal harian, dan tetapkan deadline yang realistis'
    });
  }

  if (oceanData.extraversion < 50) {
    recommendations.practical.push({
      area: 'Networking',
      suggestion: 'Mulai dengan small talk, bergabung dengan komunitas sesuai minat, dan latih public speaking'
    });
  }

  if (oceanData.agreeableness < 50) {
    recommendations.practical.push({
      area: 'Empati',
      suggestion: 'Latih active listening, tanyakan perasaan orang lain, dan praktikkan perspektif yang berbeda'
    });
  }

  if (oceanData.neuroticism > 60) {
    recommendations.practical.push({
      area: 'Manajemen Stres',
      suggestion: 'Praktikkan mindfulness, olahraga teratur, dan kembangkan support system yang kuat'
    });
  }

  return recommendations;
}
