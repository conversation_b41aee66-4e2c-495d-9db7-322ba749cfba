// Import utilities for VIA-IS assessment details and charts
import { VIA_IS_CATEGORIES, VIA_IS_STRENGTH_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultViaIsPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <button onclick="navigateTo('result')" class="text-gray-500 hover:text-gray-700 mr-4">
                ← Kembali ke Hasil
              </button>
              <h1 class="text-xl font-semibold text-gray-900">VIA-IS Character Strengths</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="navigateTo('result-riasec')" class="text-indigo-600 hover:text-indigo-700">
                RIASEC →
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg shadow p-8 mb-8 text-white">
          <div class="flex items-center space-x-6">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span class="text-4xl">💎</span>
            </div>
            <div class="flex-1">
              <h2 class="text-3xl font-bold mb-2">VIA-IS Character Strengths</h2>
              <p class="text-purple-100 text-lg leading-relaxed">
                Values in Action Inventory of Strengths (VIA-IS) mengidentifikasi 24 kekuatan karakter yang dikelompokkan dalam 6 kategori kebajikan universal.
              </p>
            </div>
          </div>
        </div>

        <!-- VIA-IS Explanation -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Tentang VIA-IS Character Strengths</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Apa itu VIA-IS?</h4>
              <div class="space-y-4 text-gray-700">
                <p>
                  VIA-IS (Values in Action Inventory of Strengths) adalah instrumen psikometri yang dikembangkan oleh 
                  Dr. Christopher Peterson dan Dr. Martin Seligman untuk mengidentifikasi kekuatan karakter seseorang.
                </p>
                <p>
                  Assessment ini mengukur 24 kekuatan karakter yang dikelompokkan dalam 6 kategori kebajikan universal 
                  yang ditemukan di berbagai budaya dan tradisi di seluruh dunia.
                </p>
                <p>
                  Kekuatan karakter ini merupakan fondasi untuk mencapai kehidupan yang bermakna, kepuasan hidup yang tinggi, 
                  dan kesejahteraan psikologis yang optimal.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Manfaat Mengetahui Character Strengths</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <span class="text-purple-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan kesadaran diri dan kepercayaan diri</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-purple-500 mt-1">✓</span>
                  <span class="text-gray-700">Membantu dalam pengembangan karir dan pemilihan pekerjaan</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-purple-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan hubungan interpersonal dan kerja tim</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-purple-500 mt-1">✓</span>
                  <span class="text-gray-700">Memberikan panduan untuk pengembangan diri</span>
                </div>
                <div class="flex items-start space-x-3">
                  <span class="text-purple-500 mt-1">✓</span>
                  <span class="text-gray-700">Meningkatkan resiliensi dan kemampuan mengatasi tantangan</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 6 Categories Overview -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">6 Kategori Kebajikan Universal</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="via-is-categories-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Large Visualization Map -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Peta Visualisasi 24 Character Strengths Anda</h3>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Categories Radar Chart -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Profil Kategori Kebajikan</h4>
              <div class="bg-gray-50 rounded-lg p-4">
                <canvas id="viaIsCategoriesRadarChart" width="400" height="400"></canvas>
              </div>
            </div>

            <!-- All Strengths Bar Chart -->
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Semua 24 Character Strengths</h4>
              <div class="bg-gray-50 rounded-lg p-4" style="height: 400px; overflow-y: auto;">
                <canvas id="viaIsStrengthsBarChart" width="400" height="800"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Top 5 Character Strengths -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Top 5 Character Strengths Anda</h3>
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4" id="top-strengths">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Detailed Categories with Strengths -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Detail Kategori dan Kekuatan Karakter</h3>
          <div class="space-y-6" id="via-is-detailed-categories">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Your Assessment Results -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">Hasil Assessment VIA-IS Anda</h3>
          <div id="via-is-user-results" class="space-y-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Navigation -->
        <div class="flex justify-between">
          <button onclick="navigateTo('result')" 
            class="bg-gray-200 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-300 transition duration-200">
            ← Kembali ke Hasil Utama
          </button>
          <button onclick="navigateTo('result-riasec')" 
            class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200">
            Lanjut ke RIASEC →
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultViaIs() {
  // Load VIA-IS assessment data
  loadViaIsData();
}

function loadViaIsData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleViaIsData();

    console.log('VIA-IS assessment results loaded:', assessmentResults);

    displayViaIsData(assessmentResults.viaIs || assessmentResults);
  } catch (error) {
    console.error('Error loading VIA-IS data:', error);
    // Fallback to sample data
    displayViaIsData(getSampleViaIsData());
  }
}

function getSampleViaIsData() {
  return {
    creativity: 88,
    curiosity: 92,
    judgment: 85,
    loveOfLearning: 90,
    perspective: 82,
    bravery: 65,
    perseverance: 78,
    honesty: 75,
    zest: 58,
    love: 62,
    kindness: 68,
    socialIntelligence: 55,
    teamwork: 48,
    fairness: 72,
    leadership: 65,
    forgiveness: 58,
    humility: 62,
    prudence: 85,
    selfRegulation: 75,
    appreciationOfBeauty: 82,
    gratitude: 68,
    hope: 72,
    humor: 55,
    spirituality: 45
  };
}

function displayViaIsData(viaIsData) {
  // Display categories overview
  displayCategoriesOverview();

  // Create visualizations
  createViaIsCategoriesRadarChart(viaIsData);
  createViaIsStrengthsBarChart(viaIsData);

  // Display top strengths
  displayTopStrengths(viaIsData);

  // Display detailed categories
  displayDetailedCategories(viaIsData);

  // Display user results
  displayUserResults(viaIsData);
}

function displayCategoriesOverview() {
  const container = document.getElementById('via-is-categories-overview');
  if (!container) return;

  container.innerHTML = Object.entries(VIA_IS_CATEGORIES).map(([key, category]) => {
    const colorClasses = ChartUtils.getViaIsCategoryColorClasses(category.color);

    return `
      <div class="border rounded-lg p-6 ${colorClasses}">
        <div class="flex items-center mb-4">
          <span class="text-3xl mr-4">${category.icon}</span>
          <div>
            <h4 class="font-semibold text-lg">${category.name}</h4>
            <p class="text-sm opacity-80">${category.description}</p>
          </div>
        </div>
        <div class="space-y-2">
          <h5 class="font-medium text-sm">Kekuatan dalam kategori:</h5>
          <div class="text-xs space-y-1">
            ${category.strengths.map(strength =>
              `<div>• ${getViaIsDisplayName(strength)}</div>`
            ).join('')}
          </div>
        </div>
      </div>
    `;
  }).join('');
}

function createViaIsCategoriesRadarChart(viaIsData) {
  const ctx = document.getElementById('viaIsCategoriesRadarChart');
  if (!ctx) return;

  return ChartUtils.createViaIsCategoriesRadarChart(ctx, viaIsData);
}

function createViaIsStrengthsBarChart(viaIsData) {
  const ctx = document.getElementById('viaIsStrengthsBarChart');
  if (!ctx) return;

  return ChartUtils.createViaIsStrengthsBarChart(ctx, viaIsData);
}

function displayTopStrengths(viaIsData) {
  const container = document.getElementById('top-strengths');
  if (!container) return;

  // Convert VIA-IS data to array and sort by score
  const strengthsArray = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    details: VIA_IS_STRENGTH_DETAILS[key]
  }));

  // Sort by score and get top 5
  const topStrengths = strengthsArray
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  container.innerHTML = topStrengths.map((strength, index) => {
    const colors = [
      'bg-yellow-100 text-yellow-800 border-yellow-300',
      'bg-orange-100 text-orange-800 border-orange-300',
      'bg-red-100 text-red-800 border-red-300',
      'bg-purple-100 text-purple-800 border-purple-300',
      'bg-indigo-100 text-indigo-800 border-indigo-300'
    ];

    return `
      <div class="border-2 rounded-lg p-4 text-center ${colors[index]}">
        <div class="text-2xl font-bold mb-2">#${index + 1}</div>
        <div class="text-lg font-semibold mb-2">${strength.displayName}</div>
        <div class="text-3xl font-bold mb-2">${strength.score}%</div>
        <div class="text-sm">${strength.details?.description || ''}</div>
      </div>
    `;
  }).join('');
}

function displayDetailedCategories(viaIsData) {
  const container = document.getElementById('via-is-detailed-categories');
  if (!container) return;

  // Calculate category averages
  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);

  // Sort categories by average score
  const sortedCategories = Object.entries(VIA_IS_CATEGORIES)
    .map(([key, category]) => ({
      key,
      category,
      averageScore: categoryAverages[key],
      strengths: category.strengths.map(strength => ({
        name: strength,
        score: viaIsData[strength] || 0,
        displayName: getViaIsDisplayName(strength),
        details: VIA_IS_STRENGTH_DETAILS[strength]
      })).sort((a, b) => b.score - a.score)
    }))
    .sort((a, b) => b.averageScore - a.averageScore);

  container.innerHTML = sortedCategories.map(({ category, averageScore, strengths }) => {
    const colorClasses = ChartUtils.getViaIsCategoryColorClasses(category.color);
    const progressBarColor = ChartUtils.getViaIsCategoryProgressBarColor(category.color);

    return `
      <div class="border rounded-lg p-6 ${colorClasses}">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <span class="text-3xl mr-4">${category.icon}</span>
            <div>
              <h4 class="font-semibold text-xl">${category.name}</h4>
              <p class="text-sm opacity-80 mt-1">${category.description}</p>
            </div>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold">${Math.round(averageScore)}%</div>
            <div class="text-sm">Skor Kategori</div>
          </div>
        </div>

        <div class="mb-6">
          <div class="w-full bg-white bg-opacity-50 rounded-full h-3">
            <div class="${progressBarColor} h-3 rounded-full transition-all duration-500" style="width: ${averageScore}%"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          ${strengths.map(strength => `
            <div class="bg-white bg-opacity-70 rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <span class="font-semibold text-sm">${strength.displayName}</span>
                <span class="text-lg font-bold">${strength.score}%</span>
              </div>
              <p class="text-xs opacity-80 leading-relaxed">${strength.details?.description || ''}</p>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }).join('');
}

function displayUserResults(viaIsData) {
  const container = document.getElementById('via-is-user-results');
  if (!container) return;

  // Get all strengths with scores
  const allStrengths = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    details: VIA_IS_STRENGTH_DETAILS[key],
    category: getCategoryForStrength(key)
  }));

  // Group by performance level
  const highStrengths = allStrengths.filter(s => s.score >= 80);
  const moderateStrengths = allStrengths.filter(s => s.score >= 60 && s.score < 80);
  const developmentStrengths = allStrengths.filter(s => s.score < 60);

  container.innerHTML = `
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- High Strengths -->
      <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <h4 class="font-semibold text-green-800 mb-4 flex items-center">
          <span class="text-green-500 mr-2">🌟</span>
          Kekuatan Utama (80%+)
        </h4>
        <p class="text-green-700 text-sm mb-4">
          Ini adalah kekuatan karakter terbaik Anda. Manfaatkan kekuatan ini dalam kehidupan sehari-hari.
        </p>
        <div class="space-y-3">
          ${highStrengths.map(strength => `
            <div class="bg-white rounded p-3">
              <div class="flex justify-between items-center mb-1">
                <span class="font-medium text-sm">${strength.displayName}</span>
                <span class="text-green-600 font-bold">${strength.score}%</span>
              </div>
              <p class="text-xs text-gray-600">${strength.details?.description || ''}</p>
            </div>
          `).join('')}
        </div>
      </div>

      <!-- Moderate Strengths -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h4 class="font-semibold text-yellow-800 mb-4 flex items-center">
          <span class="text-yellow-500 mr-2">⚡</span>
          Kekuatan Sedang (60-79%)
        </h4>
        <p class="text-yellow-700 text-sm mb-4">
          Kekuatan yang dapat dikembangkan lebih lanjut dengan latihan dan kesadaran.
        </p>
        <div class="space-y-3">
          ${moderateStrengths.map(strength => `
            <div class="bg-white rounded p-3">
              <div class="flex justify-between items-center mb-1">
                <span class="font-medium text-sm">${strength.displayName}</span>
                <span class="text-yellow-600 font-bold">${strength.score}%</span>
              </div>
              <p class="text-xs text-gray-600">${strength.details?.description || ''}</p>
            </div>
          `).join('')}
        </div>
      </div>

      <!-- Development Areas -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 class="font-semibold text-blue-800 mb-4 flex items-center">
          <span class="text-blue-500 mr-2">🎯</span>
          Area Pengembangan (<60%)
        </h4>
        <p class="text-blue-700 text-sm mb-4">
          Kekuatan yang dapat menjadi fokus pengembangan diri Anda.
        </p>
        <div class="space-y-3">
          ${developmentStrengths.map(strength => `
            <div class="bg-white rounded p-3">
              <div class="flex justify-between items-center mb-1">
                <span class="font-medium text-sm">${strength.displayName}</span>
                <span class="text-blue-600 font-bold">${strength.score}%</span>
              </div>
              <p class="text-xs text-gray-600">${strength.details?.description || ''}</p>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
}

function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Kreativitas',
    curiosity: 'Keingintahuan',
    judgment: 'Penilaian',
    loveOfLearning: 'Cinta Belajar',
    perspective: 'Perspektif',
    bravery: 'Keberanian',
    perseverance: 'Ketekunan',
    honesty: 'Kejujuran',
    zest: 'Semangat',
    love: 'Cinta',
    kindness: 'Kebaikan',
    socialIntelligence: 'Kecerdasan Sosial',
    teamwork: 'Kerja Tim',
    fairness: 'Keadilan',
    leadership: 'Kepemimpinan',
    forgiveness: 'Pengampunan',
    humility: 'Kerendahan Hati',
    prudence: 'Kehati-hatian',
    selfRegulation: 'Pengaturan Diri',
    appreciationOfBeauty: 'Apresiasi Keindahan',
    gratitude: 'Rasa Syukur',
    hope: 'Harapan',
    humor: 'Humor',
    spirituality: 'Spiritualitas'
  };

  return displayNames[key] || key;
}

function getCategoryForStrength(strengthKey) {
  for (const [categoryKey, category] of Object.entries(VIA_IS_CATEGORIES)) {
    if (category.strengths.includes(strengthKey)) {
      return categoryKey;
    }
  }
  return null;
}
